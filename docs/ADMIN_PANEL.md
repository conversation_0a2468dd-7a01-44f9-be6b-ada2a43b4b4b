# AI Navigator Admin Panel

## Overview

The AI Navigator Admin Panel is a comprehensive administrative interface for managing the platform's resources, users, and settings. It provides role-based access control and a full suite of management tools for administrators and moderators.

## Features

### 🔐 Authentication & Authorization
- **Role-based access control** using existing user roles (ADMIN, MODERATOR, USER)
- **Route protection** with `AdminRouteGuard` component
- **Permission-based UI** showing/hiding features based on user permissions
- **Secure API calls** with JWT token authentication

### 📊 Dashboard
- **Key metrics overview** with statistics cards
- **Recent activity feed** showing platform events
- **Visual indicators** for pending items requiring attention
- **Quick navigation** to different admin sections

### 👥 User Management
- **User listing** with pagination and filtering
- **Role assignment** (User, Moderator, Admin)
- **Status management** (Active, Suspended, Deactivated)
- **User search** by name, email, or username
- **Contact users** via email integration

### 📝 Entity Management
- **Entity listing** with comprehensive filtering
- **Status updates** (Pending, Active, Rejected, Inactive, Archived)
- **Bulk actions** for efficient management
- **Entity preview** and external link access
- **Search and filter** by type, status, and content

### 🏷️ Content Organization
- **Category management** with CRUD operations
- **Tag management** with usage statistics
- **Inline editing** for quick updates
- **Usage tracking** showing entity counts per category/tag

### 💬 Tool Request Management
- **Request queue** with priority and status tracking
- **Review workflow** (Pending → Under Review → Approved/Rejected)
- **User communication** capabilities
- **Request details** with full context

### ⚙️ Settings Management
- **Application settings** organized by category
- **LLM provider configuration** for AI features
- **System preferences** with real-time updates
- **Security settings** for platform protection

### 📈 Analytics (Framework)
- **Metrics dashboard** with key performance indicators
- **Chart placeholders** ready for visualization library integration
- **Activity summaries** showing platform health
- **Growth tracking** capabilities

## Technical Implementation

### Architecture
```
src/
├── app/admin/                    # Admin routes
│   ├── page.tsx                 # Dashboard
│   ├── entities/page.tsx        # Entity management
│   ├── users/page.tsx           # User management
│   ├── tool-requests/page.tsx   # Tool requests
│   ├── categories/page.tsx      # Category management
│   ├── tags/page.tsx           # Tag management
│   ├── settings/page.tsx       # Settings
│   └── analytics/page.tsx      # Analytics
├── components/admin/            # Admin components
│   ├── AdminLayout.tsx         # Main admin layout
│   └── AdminRouteGuard.tsx     # Route protection
├── hooks/
│   └── useAdminAuth.ts         # Admin authentication hook
└── services/api.ts             # Admin API functions
```

### Key Components

#### AdminRouteGuard
Protects admin routes based on user permissions:
```typescript
<AdminRouteGuard requiredPermission="canManageUsers">
  <AdminContent />
</AdminRouteGuard>
```

#### AdminLayout
Provides consistent layout with:
- Responsive sidebar navigation
- User context display
- Permission-based menu items
- Mobile-friendly design

#### useAdminAuth Hook
Manages admin authentication state:
```typescript
const { 
  isAdmin, 
  isModerator, 
  canManageUsers, 
  canManageEntities 
} = useAdminAuth();
```

### API Integration

The admin panel integrates with existing backend endpoints:

- **User Management**: `/admin/users/*`
- **Entity Management**: `/admin/entities/*`
- **Settings**: `/admin/settings/*`
- **Categories**: `/admin/categories/*`
- **Tags**: `/admin/tags/*`

### Permissions System

| Permission | Admin | Moderator | Description |
|------------|-------|-----------|-------------|
| `canManageUsers` | ✅ | ❌ | User role/status management |
| `canManageEntities` | ✅ | ✅ | Entity approval/rejection |
| `canManageSettings` | ✅ | ❌ | System configuration |
| `canViewAnalytics` | ✅ | ✅ | Platform metrics access |

## Usage

### Accessing the Admin Panel

1. **Login** with an admin or moderator account
2. **Navigate** to the admin panel via the user dropdown menu
3. **Use the sidebar** to access different management sections

### Managing Entities

1. Go to **Entity Management**
2. **Filter** by status, type, or search terms
3. **Review** pending submissions
4. **Update status** using the actions dropdown
5. **Bulk actions** for multiple entities

### User Administration

1. Access **User Management**
2. **Search** for specific users
3. **Update roles** or status as needed
4. **Contact users** directly via email

### Content Organization

1. **Categories**: Create, edit, and organize entity categories
2. **Tags**: Manage tags with usage statistics
3. **Inline editing** for quick updates

## Security Features

- **JWT-based authentication** for all admin operations
- **Role-based access control** at component and API level
- **Route protection** preventing unauthorized access
- **Audit trails** for administrative actions (framework ready)
- **Confirmation dialogs** for destructive operations

## Future Enhancements

### Planned Features
- **Advanced analytics** with chart visualizations
- **Audit logging** for all admin actions
- **Bulk import/export** capabilities
- **Advanced filtering** with saved filter sets
- **Real-time notifications** for admin events
- **Content moderation** tools with AI assistance

### Integration Opportunities
- **Chart libraries** (Chart.js, Recharts, D3.js)
- **Real-time updates** with WebSocket integration
- **Email templates** for user communication
- **Backup/restore** functionality
- **API rate limiting** and monitoring

## Development Notes

### Adding New Admin Features

1. **Create route** in `/app/admin/`
2. **Add navigation** item in `AdminLayout.tsx`
3. **Implement API** functions in `services/api.ts`
4. **Add permissions** check in `useAdminAuth.ts`
5. **Protect route** with `AdminRouteGuard`

### Styling Guidelines

- Use **Tailwind CSS** for consistent styling
- Follow **mobile-first** responsive design
- Maintain **accessibility** standards (WCAG 2.1 AA)
- Use **Lucide icons** for consistency
- Apply **shadcn/ui** components where possible

### Testing Considerations

- **Role-based access** testing
- **API integration** testing
- **Responsive design** testing
- **Security** testing for unauthorized access
- **Performance** testing with large datasets

## Support

For questions or issues with the admin panel:

1. Check the **API documentation** at `/api-docs`
2. Review **component documentation** in source files
3. Test with **different user roles** to verify permissions
4. Monitor **browser console** for client-side errors
5. Check **server logs** for API-related issues
