# Backend Entity Type Filters Specification

## Overview

This document specifies the expected format and validation requirements for the `entity_type_filters` parameter in the `/entities` API endpoint.

## Current Issue

The frontend is sending entity-specific filters but the backend is rejecting them with errors like:
```
entity_type_filters.property course should not exist
```

This suggests a mismatch between the expected and actual data structure.

## Expected Request Format

### Query Parameter
- **Parameter Name**: `entity_type_filters`
- **Type**: JSON string
- **Required**: No

### JSON Structure
The `entity_type_filters` parameter should contain a JSON object where:
- **Keys**: Entity type identifiers (snake_case)
- **Values**: Filter objects specific to each entity type

```json
{
  "entity_type_filters": "{\"course\":{\"skill_levels\":[\"BEGINNER\",\"INTERMEDIATE\"],\"certificate_available\":true},\"job\":{\"employment_types\":[\"Full-time\",\"Remote\"],\"salary_min\":50000}}"
}
```

When parsed, the JSON should have this structure:
```json
{
  "course": {
    "skill_levels": ["BEGINNER", "INTERMEDIATE"],
    "certificate_available": true
  },
  "job": {
    "employment_types": ["Full-time", "Remote"],
    "salary_min": 50000
  }
}
```

## Entity Type Keys

The following entity type keys are supported:

| Frontend Display Name | Backend Key | Description |
|----------------------|-------------|-------------|
| AI Tool | `tool` | AI tools, APIs, models, datasets, libraries, services |
| Course | `course` | Educational courses and training programs |
| Job | `job` | Job listings and career opportunities |
| Hardware | `hardware` | AI hardware and computing equipment |
| Event | `event` | Conferences, workshops, webinars, meetups |
| Agency | `agency` | AI consulting agencies and service providers |
| Software | `software` | Software platforms and applications |
| Research Paper | `research_paper` | Academic papers and research publications |
| Podcast | `podcast` | AI-related podcasts and audio content |
| Community | `community` | Online communities and forums |
| Grant | `grant` | Funding opportunities and grants |
| Newsletter | `newsletter` | AI newsletters and publications |
| Book | `book` | Books and written resources |

## Filter Specifications by Entity Type

### Tool Filters (`tool`)
```json
{
  "technical_levels": ["BEGINNER", "INTERMEDIATE", "ADVANCED", "EXPERT"],
  "learning_curves": ["EASY", "MODERATE", "STEEP"],
  "pricing_models": ["FREE", "FREEMIUM", "SUBSCRIPTION", "ONE_TIME"],
  "price_ranges": ["FREE", "UNDER_10", "UNDER_50", "UNDER_100", "OVER_100"],
  "has_api": true,
  "has_free_tier": false,
  "open_source": true,
  "mobile_support": false,
  "demo_available": true,
  "platforms": ["Web", "iOS", "Android"],
  "integrations": ["Slack", "Discord", "API"],
  "frameworks": ["TensorFlow", "PyTorch"],
  "libraries": ["OpenAI", "Anthropic"]
}
```

### Course Filters (`course`)
```json
{
  "skill_levels": ["BEGINNER", "INTERMEDIATE", "ADVANCED", "EXPERT"],
  "certificate_available": true,
  "instructor_name": "John Doe",
  "duration_text": "10 hours",
  "enrollment_min": 100,
  "enrollment_max": 10000,
  "prerequisites": "Basic programming knowledge",
  "has_syllabus": true
}
```

### Job Filters (`job`)
```json
{
  "employment_types": ["Full-time", "Part-time", "Contract", "Freelance", "Internship"],
  "experience_levels": ["Entry", "Mid", "Senior", "Lead", "Executive"],
  "location_types": ["Remote", "On-site", "Hybrid"],
  "company_name": "OpenAI",
  "job_title": "AI Engineer",
  "salary_min": 50000,
  "salary_max": 200000,
  "job_description": "Machine learning engineer",
  "has_application_url": true
}
```

### Hardware Filters (`hardware`)
```json
{
  "hardware_types": ["GPU", "CPU", "FPGA", "TPU", "ASIC"],
  "manufacturers": ["NVIDIA", "Intel", "AMD", "Apple"],
  "release_date_from": "2023-01-01",
  "release_date_to": "2024-12-31",
  "price_min": 100,
  "price_max": 5000,
  "specifications_search": "GDDR6",
  "has_datasheet": true,
  "memory_search": "16GB",
  "processor_search": "Intel i9"
}
```

### Event Filters (`event`)
```json
{
  "event_types": ["Conference", "Workshop", "Webinar", "Meetup", "Hackathon"],
  "start_date_from": "2024-01-01",
  "start_date_to": "2024-12-31",
  "end_date_from": "2024-01-01",
  "end_date_to": "2024-12-31",
  "is_online": true,
  "location": "San Francisco, CA",
  "price_text": "Free",
  "registration_required": true,
  "has_registration_url": true,
  "speakers_search": "Andrew Ng",
  "target_audience_search": "developers"
}
```

## Validation Requirements

### Structure Validation
1. **Top-level object**: Must be a valid JSON object
2. **Entity type keys**: Must be valid snake_case entity type identifiers
3. **Filter values**: Must be objects with valid filter properties
4. **No extra properties**: Reject objects with unexpected top-level keys like `property`

### Data Type Validation
- **Arrays**: Must contain valid enum values where applicable
- **Booleans**: Must be `true` or `false`
- **Numbers**: Must be valid integers/floats within reasonable ranges
- **Strings**: Must be non-empty strings for text fields
- **Dates**: Must be valid ISO date strings (YYYY-MM-DD)

### Business Logic Validation
- **Date ranges**: `from` dates must be before `to` dates
- **Numeric ranges**: `min` values must be less than `max` values
- **Enum values**: Must match predefined options for each field

## Error Handling

### Invalid Structure Errors
```json
{
  "statusCode": 400,
  "message": "entity_type_filters.property course should not exist",
  "error": "Bad Request"
}
```

### Validation Errors
```json
{
  "statusCode": 400,
  "message": [
    "entity_type_filters.course.skill_levels must contain only valid values: BEGINNER, INTERMEDIATE, ADVANCED, EXPERT",
    "entity_type_filters.job.salary_min must be a positive number"
  ],
  "error": "Bad Request"
}
```

## Implementation Notes

1. **Empty filters**: Empty filter objects should be ignored
2. **Unknown entity types**: Should be filtered out, not cause errors
3. **Partial filters**: Valid filters should be applied even if some are invalid
4. **Case sensitivity**: Entity type keys are case-sensitive (use snake_case)
5. **Null values**: Should be treated as filter not applied

## Testing Examples

### Valid Request
```
GET /entities?entity_type_filters={"course":{"skill_levels":["BEGINNER"],"certificate_available":true}}
```

### Invalid Request (causes current error)
```
GET /entities?entity_type_filters={"property":"course"}
```

### Multiple Entity Types
```
GET /entities?entity_type_filters={"course":{"skill_levels":["BEGINNER"]},"job":{"employment_types":["Full-time"]}}
```
