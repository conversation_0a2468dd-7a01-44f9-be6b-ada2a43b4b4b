# Backend Issues & Required Fixes

## ✅ RESOLVED: Missing upvoteCount Field in Entity Responses

**Issue:** ~~The upvote system is failing because the backend API endpoints do not return the `upvoteCount` field in entity responses.~~ **FIXED!**

**Status:** ✅ **COMPLETELY RESOLVED** - Backend team has successfully implemented all required changes.

**Affected Endpoints:**
- `GET /entities` - List entities endpoint
- `GET /entities/{id}` - Single entity endpoint

**Current Response Structure:**
```json
{
  "id": "f0f4a70f-1dc9-4921-b14e-06861bc67cab",
  "name": "<PERSON>ime AI",
  "slug": "rime-ai",
  "avgRating": 0,
  "reviewCount": 0,
  "saveCount": 0,
  // ❌ MISSING: upvoteCount field
}
```

**Required Response Structure:**
```json
{
  "id": "f0f4a70f-1dc9-4921-b14e-06861bc67cab",
  "name": "<PERSON><PERSON> AI",
  "slug": "rime-ai",
  "avgRating": 0,
  "reviewCount": 0,
  "saveCount": 0,
  "upvoteCount": 0  // ✅ REQUIRED: Add this field
}
```

**Impact:**
- Upvote counts reset to 0 on page refresh
- Users cannot see actual upvote counts
- Optimistic UI updates don't persist
- Poor user experience with upvote functionality

**Backend Tasks Required:**
1. Add `upvoteCount` field to entity response DTOs
2. Calculate upvote count from the upvotes table/relationship
3. Include upvoteCount in both list and single entity endpoints
4. Ensure upvoteCount is updated when upvotes are added/removed

**Database Query Example:**
```sql
-- Ensure entities include upvote count
SELECT 
  e.*,
  COUNT(u.id) as upvote_count
FROM entities e
LEFT JOIN upvotes u ON e.id = u.entity_id
GROUP BY e.id;
```

**Priority:** HIGH - This affects core user engagement functionality

**Frontend Workaround Applied:**
- Added null safety for missing upvoteCount (defaults to 0)
- Improved error handling and user feedback
- Added visual error indicators

**Testing:**
After backend fix, verify:
- `GET /entities` returns upvoteCount for each entity
- `GET /entities/{id}` returns upvoteCount for the entity
- upvoteCount reflects actual number of upvotes in database
- upvoteCount updates when POST/DELETE /entities/{id}/upvote is called

---

## Other Backend Issues

### API Documentation
- Ensure OpenAPI spec includes upvoteCount field in entity schemas
- Update example responses to include upvoteCount

### Performance Considerations
- Consider caching upvote counts for better performance
- Ensure upvote count queries are optimized
