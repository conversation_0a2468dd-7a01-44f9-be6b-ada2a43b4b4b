<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Navigator Filter Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .test-error {
            background: #ffe8e8;
            color: #d00;
        }
        .test-success {
            background: #e8f5e8;
            color: #080;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .filter-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .filter-group {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
        }
        .filter-group h4 {
            margin-top: 0;
            color: #333;
        }
        input[type="checkbox"] {
            margin-right: 8px;
        }
        label {
            display: block;
            margin: 8px 0;
            cursor: pointer;
        }
        #urlOutput {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 AI Navigator Filter System Test</h1>
    
    <div class="test-section">
        <h2>Filter URL Parameter Testing</h2>
        <p>This test validates the new filter parameters and URL construction logic.</p>
        
        <div class="filter-demo">
            <div class="filter-group">
                <h4>📋 Business & Pricing</h4>
                <label>
                    <input type="checkbox" id="hasFreeTier" onchange="updateFilters()">
                    Has Free Tier
                </label>
                <label>
                    <input type="checkbox" id="hasApiDocumentation" onchange="updateFilters()">
                    Has API Documentation
                </label>
                <label>
                    <input type="checkbox" id="affiliateApproved" onchange="updateFilters()">
                    Affiliate: Approved
                </label>
                <label>
                    <input type="checkbox" id="affiliateApplied" onchange="updateFilters()">
                    Affiliate: Applied
                </label>
            </div>
            
            <div class="filter-group">
                <h4>⚡ Technical Details</h4>
                <label>
                    <input type="checkbox" id="techBeginner" onchange="updateFilters()">
                    Beginner Level
                </label>
                <label>
                    <input type="checkbox" id="techIntermediate" onchange="updateFilters()">
                    Intermediate Level
                </label>
                <label>
                    <input type="checkbox" id="techAdvanced" onchange="updateFilters()">
                    Advanced Level
                </label>
                <label>
                    <input type="checkbox" id="apiAccess" onchange="updateFilters()">
                    API Access
                </label>
            </div>
            
            <div class="filter-group">
                <h4>🔗 Integrations</h4>
                <label>
                    <input type="checkbox" id="intGitHub" onchange="updateFilters()">
                    GitHub
                </label>
                <label>
                    <input type="checkbox" id="intSlack" onchange="updateFilters()">
                    Slack
                </label>
                <label>
                    <input type="checkbox" id="intZapier" onchange="updateFilters()">
                    Zapier
                </label>
            </div>
            
            <div class="filter-group">
                <h4>💻 Platforms</h4>
                <label>
                    <input type="checkbox" id="platWeb" onchange="updateFilters()">
                    Web
                </label>
                <label>
                    <input type="checkbox" id="platMacOS" onchange="updateFilters()">
                    macOS
                </label>
                <label>
                    <input type="checkbox" id="platWindows" onchange="updateFilters()">
                    Windows
                </label>
            </div>
        </div>
        
        <h3>Generated URL Parameters:</h3>
        <div id="urlOutput">No filters selected</div>
        
        <button onclick="clearAllFilters()">Clear All Filters</button>
        <button onclick="testAllFilters()">Test All Filters</button>
        <button onclick="runValidationTests()">Run Validation Tests</button>
    </div>
    
    <div class="test-section">
        <h2>Test Results</h2>
        <div id="testResults"></div>
    </div>

    <script>
        function updateFilters() {
            const params = new URLSearchParams();
            
            // Business & Pricing filters
            if (document.getElementById('hasFreeTier').checked) {
                params.append('hasFreeTier', 'true');
            }
            if (document.getElementById('hasApiDocumentation').checked) {
                params.append('has_api_documentation', 'true');
            }
            
            // Affiliate status array
            const affiliateStatuses = [];
            if (document.getElementById('affiliateApproved').checked) {
                affiliateStatuses.push('APPROVED');
            }
            if (document.getElementById('affiliateApplied').checked) {
                affiliateStatuses.push('APPLIED');
            }
            if (affiliateStatuses.length > 0) {
                affiliateStatuses.forEach(status => params.append('affiliateStatus', status));
            }
            
            // Technical levels array
            const techLevels = [];
            if (document.getElementById('techBeginner').checked) {
                techLevels.push('BEGINNER');
            }
            if (document.getElementById('techIntermediate').checked) {
                techLevels.push('INTERMEDIATE');
            }
            if (document.getElementById('techAdvanced').checked) {
                techLevels.push('ADVANCED');
            }
            if (techLevels.length > 0) {
                techLevels.forEach(level => params.append('technical_levels', level));
            }
            
            if (document.getElementById('apiAccess').checked) {
                params.append('apiAccess', 'true');
            }
            
            // Integrations array
            const integrations = [];
            if (document.getElementById('intGitHub').checked) {
                integrations.push('GitHub');
            }
            if (document.getElementById('intSlack').checked) {
                integrations.push('Slack');
            }
            if (document.getElementById('intZapier').checked) {
                integrations.push('Zapier');
            }
            if (integrations.length > 0) {
                integrations.forEach(int => params.append('integrations', int));
            }
            
            // Platforms array
            const platforms = [];
            if (document.getElementById('platWeb').checked) {
                platforms.push('Web');
            }
            if (document.getElementById('platMacOS').checked) {
                platforms.push('macOS');
            }
            if (document.getElementById('platWindows').checked) {
                platforms.push('Windows');
            }
            if (platforms.length > 0) {
                platforms.forEach(plat => params.append('platforms', plat));
            }
            
            const urlString = params.toString();
            document.getElementById('urlOutput').textContent = urlString || 'No filters selected';
        }
        
        function clearAllFilters() {
            document.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.checked = false);
            updateFilters();
        }
        
        function testAllFilters() {
            document.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.checked = true);
            updateFilters();
        }
        
        function runValidationTests() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<h3>Running Validation Tests...</h3>';
            
            const tests = [
                {
                    name: 'Single Boolean Filter',
                    setup: () => {
                        clearAllFilters();
                        document.getElementById('hasApiDocumentation').checked = true;
                        updateFilters();
                    },
                    expected: 'has_api_documentation=true',
                    actual: () => document.getElementById('urlOutput').textContent
                },
                {
                    name: 'Array Filter - Affiliate Status',
                    setup: () => {
                        clearAllFilters();
                        document.getElementById('affiliateApproved').checked = true;
                        document.getElementById('affiliateApplied').checked = true;
                        updateFilters();
                    },
                    expected: 'affiliateStatus=APPROVED&affiliateStatus=APPLIED',
                    actual: () => document.getElementById('urlOutput').textContent
                },
                {
                    name: 'Array Filter - Technical Levels',
                    setup: () => {
                        clearAllFilters();
                        document.getElementById('techBeginner').checked = true;
                        document.getElementById('techIntermediate').checked = true;
                        updateFilters();
                    },
                    expected: 'technical_levels=BEGINNER&technical_levels=INTERMEDIATE',
                    actual: () => document.getElementById('urlOutput').textContent
                },
                {
                    name: 'Combined Filters',
                    setup: () => {
                        clearAllFilters();
                        document.getElementById('hasFreeTier').checked = true;
                        document.getElementById('hasApiDocumentation').checked = true;
                        document.getElementById('techIntermediate').checked = true;
                        document.getElementById('intGitHub').checked = true;
                        updateFilters();
                    },
                    expected: 'hasFreeTier=true&has_api_documentation=true&technical_levels=INTERMEDIATE&integrations=GitHub',
                    actual: () => document.getElementById('urlOutput').textContent
                }
            ];
            
            let passedTests = 0;
            let totalTests = tests.length;
            
            tests.forEach((test, index) => {
                test.setup();
                const actual = test.actual();
                const passed = actual === test.expected;
                
                if (passed) passedTests++;
                
                results.innerHTML += `
                    <div class="test-result ${passed ? 'test-success' : 'test-error'}">
                        <strong>Test ${index + 1}: ${test.name}</strong><br>
                        Expected: ${test.expected}<br>
                        Actual: ${actual}<br>
                        Status: ${passed ? '✅ PASSED' : '❌ FAILED'}
                    </div>
                `;
            });
            
            results.innerHTML += `
                <div class="test-result ${passedTests === totalTests ? 'test-success' : 'test-error'}">
                    <strong>Summary: ${passedTests}/${totalTests} tests passed</strong>
                </div>
            `;
            
            clearAllFilters();
        }
        
        // Initialize
        updateFilters();
    </script>
</body>
</html>
