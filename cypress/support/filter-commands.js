// Custom Cypress commands for filter testing

/**
 * Apply a search filter
 * @param {string} searchTerm - The search term to apply
 */
Cypress.Commands.add('applySearchFilter', (searchTerm) => {
  cy.get('[data-testid="search-input"]').clear().type(searchTerm);
  cy.get('[data-testid="search-form"]').submit();
});

/**
 * Apply entity type filters
 * @param {string[]} entityTypes - Array of entity type data-testids
 */
Cypress.Commands.add('applyEntityTypeFilters', (entityTypes) => {
  entityTypes.forEach(entityType => {
    cy.get(`[data-testid="entity-type-${entityType}"]`).click();
  });
});

/**
 * Apply category filters
 * @param {string[]} categories - Array of category data-testids
 */
Cypress.Commands.add('applyCategoryFilters', (categories) => {
  categories.forEach(category => {
    cy.get(`[data-testid="category-${category}"]`).click();
  });
});

/**
 * Open advanced filters section
 */
Cypress.Commands.add('openAdvancedFilters', () => {
  cy.get('[data-testid="advanced-filters-toggle"]').click();
  cy.get('[data-testid="advanced-filters"]').should('be.visible');
});

/**
 * Apply rating filter
 * @param {number} min - Minimum rating
 * @param {number} max - Maximum rating
 */
Cypress.Commands.add('applyRatingFilter', (min, max) => {
  cy.openAdvancedFilters();
  if (min !== undefined) {
    cy.get('[data-testid="rating-min-input"]').clear().type(min.toString());
  }
  if (max !== undefined) {
    cy.get('[data-testid="rating-max-input"]').clear().type(max.toString());
  }
});

/**
 * Apply business filters
 * @param {Object} options - Business filter options
 */
Cypress.Commands.add('applyBusinessFilters', (options = {}) => {
  cy.openAdvancedFilters();
  
  if (options.hasFreeTier) {
    cy.get('[data-testid="has-free-tier-checkbox"]').check();
  }
  
  if (options.apiAccess) {
    cy.get('[data-testid="api-access-checkbox"]').check();
  }
  
  if (options.pricingModels && options.pricingModels.length > 0) {
    options.pricingModels.forEach(model => {
      cy.get(`[data-testid="pricing-model-${model.toLowerCase()}"]`).click();
    });
  }
});

/**
 * Apply date range filters
 * @param {string} fromDate - Start date (YYYY-MM-DD)
 * @param {string} toDate - End date (YYYY-MM-DD)
 */
Cypress.Commands.add('applyDateRangeFilter', (fromDate, toDate) => {
  cy.openAdvancedFilters();
  
  if (fromDate) {
    cy.get('[data-testid="date-from-input"]').type(fromDate);
  }
  
  if (toDate) {
    cy.get('[data-testid="date-to-input"]').type(toDate);
  }
});

/**
 * Apply platform filters
 * @param {string[]} platforms - Array of platform names
 */
Cypress.Commands.add('applyPlatformFilters', (platforms) => {
  cy.openAdvancedFilters();
  
  platforms.forEach(platform => {
    cy.get(`[data-testid="platform-${platform.toLowerCase()}"]`).click();
  });
});

/**
 * Apply integration filters
 * @param {string[]} integrations - Array of integration names
 */
Cypress.Commands.add('applyIntegrationFilters', (integrations) => {
  cy.openAdvancedFilters();
  
  integrations.forEach(integration => {
    cy.get(`[data-testid="integration-${integration.toLowerCase()}"]`).click();
  });
});

/**
 * Clear all filters
 */
Cypress.Commands.add('clearAllFilters', () => {
  cy.get('[data-testid="clear-all-filters"]').click();
});

/**
 * Clear advanced filters only
 */
Cypress.Commands.add('clearAdvancedFilters', () => {
  cy.get('[data-testid="clear-advanced-filters"]').click();
});

/**
 * Verify active filters contain specific text
 * @param {string[]} expectedFilters - Array of expected filter texts
 */
Cypress.Commands.add('verifyActiveFilters', (expectedFilters) => {
  cy.get('[data-testid="active-filters"]').should('be.visible');
  
  expectedFilters.forEach(filter => {
    cy.get('[data-testid="active-filters"]').should('contain', filter);
  });
});

/**
 * Verify URL contains specific parameters
 * @param {Object} expectedParams - Object with expected URL parameters
 */
Cypress.Commands.add('verifyUrlParams', (expectedParams) => {
  Object.entries(expectedParams).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach(v => {
        cy.url().should('include', `${key}=${encodeURIComponent(v)}`);
      });
    } else {
      cy.url().should('include', `${key}=${encodeURIComponent(value)}`);
    }
  });
});

/**
 * Verify results count is within expected range
 * @param {number} min - Minimum expected results
 * @param {number} max - Maximum expected results (optional)
 */
Cypress.Commands.add('verifyResultsCount', (min, max) => {
  cy.get('[data-testid="results-count"]').should('be.visible');
  
  if (max !== undefined) {
    cy.get('[data-testid="results-count"]').invoke('text').then(text => {
      const count = parseInt(text.match(/\d+/)[0]);
      expect(count).to.be.at.least(min);
      expect(count).to.be.at.most(max);
    });
  } else {
    cy.get('[data-testid="results-count"]').invoke('text').then(text => {
      const count = parseInt(text.match(/\d+/)[0]);
      expect(count).to.be.at.least(min);
    });
  }
});

/**
 * Wait for filter results to load
 * @param {number} timeout - Timeout in milliseconds (default: 10000)
 */
Cypress.Commands.add('waitForFilterResults', (timeout = 10000) => {
  cy.get('[data-testid="results-loading"]', { timeout }).should('not.exist');
  cy.get('[data-testid="results-grid"]', { timeout }).should('be.visible');
});

/**
 * Apply entity-specific filters for courses
 * @param {Object} options - Course filter options
 */
Cypress.Commands.add('applyCourseFilters', (options = {}) => {
  // First select course entity type
  cy.applyEntityTypeFilters(['course']);
  
  if (options.skillLevels && options.skillLevels.length > 0) {
    options.skillLevels.forEach(level => {
      cy.get(`[data-testid="skill-level-${level.toLowerCase()}"]`).click();
    });
  }
  
  if (options.certificateAvailable) {
    cy.get('[data-testid="certificate-available"]').check();
  }
  
  if (options.instructorName) {
    cy.get('[data-testid="instructor-name-input"]').type(options.instructorName);
  }
  
  if (options.duration) {
    cy.get('[data-testid="duration-input"]').type(options.duration);
  }
  
  if (options.enrollmentMin) {
    cy.get('[data-testid="enrollment-min-input"]').type(options.enrollmentMin.toString());
  }
  
  if (options.enrollmentMax) {
    cy.get('[data-testid="enrollment-max-input"]').type(options.enrollmentMax.toString());
  }
});

/**
 * Apply entity-specific filters for jobs
 * @param {Object} options - Job filter options
 */
Cypress.Commands.add('applyJobFilters', (options = {}) => {
  // First select job entity type
  cy.applyEntityTypeFilters(['job']);
  
  if (options.employmentTypes && options.employmentTypes.length > 0) {
    options.employmentTypes.forEach(type => {
      cy.get(`[data-testid="employment-type-${type.toLowerCase()}"]`).click();
    });
  }
  
  if (options.experienceLevels && options.experienceLevels.length > 0) {
    options.experienceLevels.forEach(level => {
      cy.get(`[data-testid="experience-level-${level.toLowerCase()}"]`).click();
    });
  }
  
  if (options.locationTypes && options.locationTypes.length > 0) {
    options.locationTypes.forEach(type => {
      cy.get(`[data-testid="location-type-${type.toLowerCase()}"]`).click();
    });
  }
  
  if (options.companyName) {
    cy.get('[data-testid="company-name-input"]').type(options.companyName);
  }
  
  if (options.salaryMin) {
    cy.get('[data-testid="salary-min-input"]').type(options.salaryMin.toString());
  }
  
  if (options.salaryMax) {
    cy.get('[data-testid="salary-max-input"]').type(options.salaryMax.toString());
  }
});

/**
 * Apply entity-specific filters for tools
 * @param {Object} options - Tool filter options
 */
Cypress.Commands.add('applyToolFilters', (options = {}) => {
  // First select tool entity type
  cy.applyEntityTypeFilters(['tool']);
  
  if (options.technicalLevels && options.technicalLevels.length > 0) {
    options.technicalLevels.forEach(level => {
      cy.get(`[data-testid="technical-level-${level.toLowerCase()}"]`).click();
    });
  }
  
  if (options.learningCurves && options.learningCurves.length > 0) {
    options.learningCurves.forEach(curve => {
      cy.get(`[data-testid="learning-curve-${curve.toLowerCase()}"]`).click();
    });
  }
  
  if (options.hasApi) {
    cy.get('[data-testid="has-api"]').check();
  }
  
  if (options.openSource) {
    cy.get('[data-testid="open-source"]').check();
  }
  
  if (options.mobileSupport) {
    cy.get('[data-testid="mobile-support"]').check();
  }
});

/**
 * Test mobile filter functionality
 */
Cypress.Commands.add('testMobileFilters', () => {
  // Set mobile viewport
  cy.viewport('iphone-x');
  
  // Open mobile filter sheet
  cy.get('[data-testid="mobile-filter-toggle"]').click();
  cy.get('[data-testid="mobile-filter-sheet"]').should('be.visible');
  
  // Apply a filter
  cy.get('[data-testid="entity-type-option"]').first().click();
  
  // Close filter sheet
  cy.get('[data-testid="close-filter-sheet"]').click();
  
  // Verify filter was applied
  cy.get('[data-testid="mobile-filter-badge"]').should('contain', '1');
});
