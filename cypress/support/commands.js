/**
 * Custom Cypress Commands for Entity Filter Testing
 */

// Command to select entity type and wait for API response
Cypress.Commands.add('selectEntityType', (entityTypeName) => {
  cy.get('[data-testid="entity-type-filter"]').contains(entityTypeName).click();
  cy.wait('@getEntities');
});

// Command to apply entity-specific filter and verify API call
Cypress.Commands.add('applyEntityFilter', (entityType, filterName, filterValue, expectedBackendKey) => {
  cy.get(`[data-testid="${filterName}"]`).within(() => {
    if (typeof filterValue === 'boolean') {
      if (filterValue) {
        cy.get('input[type="checkbox"]').check();
      } else {
        cy.get('input[type="checkbox"]').uncheck();
      }
    } else if (Array.isArray(filterValue)) {
      filterValue.forEach(value => {
        cy.get(`input[value="${value}"]`).check();
      });
    } else {
      cy.get('input').clear().type(filterValue.toString());
    }
  });

  cy.wait('@getEntities').then((interception) => {
    const url = new URL(interception.request.url);
    const entityFilters = url.searchParams.get('entity_type_filters');
    
    expect(entityFilters).to.not.be.null;
    const parsed = JSON.parse(entityFilters);
    expect(parsed).to.have.property(expectedBackendKey || entityType.toLowerCase());
    
    return parsed;
  });
});

// Command to verify entity filter in API request
Cypress.Commands.add('verifyEntityFilterInRequest', (entityType, filterKey, expectedValue) => {
  cy.wait('@getEntities').then((interception) => {
    const url = new URL(interception.request.url);
    const entityFilters = url.searchParams.get('entity_type_filters');
    
    expect(entityFilters).to.not.be.null;
    const parsed = JSON.parse(entityFilters);
    expect(parsed).to.have.property(entityType);
    
    if (Array.isArray(expectedValue)) {
      expect(parsed[entityType][filterKey]).to.include.members(expectedValue);
    } else {
      expect(parsed[entityType][filterKey]).to.equal(expectedValue);
    }
  });
});

// Command to clear all filters and verify
Cypress.Commands.add('clearAllFilters', () => {
  cy.get('[data-testid="clear-all-filters"]').click();
  cy.wait('@getEntities');
  
  // Verify URL doesn't contain entity filters
  cy.url().should('not.include', 'entity_type_filters');
});

// Command to verify filter persistence after page reload
Cypress.Commands.add('verifyFilterPersistence', (entityType, filterTestId, expectedValue) => {
  // Check URL contains the filters
  cy.url().should('include', 'entity_type_filters');

  // Reload page
  cy.reload();
  cy.wait('@getEntities');

  // Verify filters are restored
  cy.get(`[data-testid="${filterTestId}"]`).within(() => {
    if (typeof expectedValue === 'boolean' && expectedValue) {
      cy.get('input[type="checkbox"]').should('be.checked');
    } else if (Array.isArray(expectedValue)) {
      expectedValue.forEach(value => {
        cy.get(`input[value="${value}"]`).should('be.checked');
      });
    } else {
      cy.get('input').should('have.value', expectedValue.toString());
    }
  });
});

// Command to test filter validation
Cypress.Commands.add('testFilterValidation', (filterTestId, invalidValue, validValue) => {
  // Enter invalid value
  cy.get(`[data-testid="${filterTestId}"]`).within(() => {
    cy.get('input').clear().type(invalidValue);
  });

  // Check for validation error (if implemented)
  cy.get('[data-testid="validation-error"]').should('be.visible');

  // Enter valid value
  cy.get(`[data-testid="${filterTestId}"]`).within(() => {
    cy.get('input').clear().type(validValue);
  });

  // Validation error should disappear
  cy.get('[data-testid="validation-error"]').should('not.exist');
});

// Command to test range filters (min/max)
Cypress.Commands.add('testRangeFilter', (minTestId, maxTestId, minValue, maxValue, entityType, minKey, maxKey) => {
  cy.get(`[data-testid="${minTestId}"]`).type(minValue.toString());
  cy.get(`[data-testid="${maxTestId}"]`).type(maxValue.toString());

  cy.wait('@getEntities').then((interception) => {
    const url = new URL(interception.request.url);
    const entityFilters = url.searchParams.get('entity_type_filters');
    const parsed = JSON.parse(entityFilters);
    
    expect(parsed[entityType][minKey]).to.equal(minValue);
    expect(parsed[entityType][maxKey]).to.equal(maxValue);
  });
});

// Command to test date range filters
Cypress.Commands.add('testDateRangeFilter', (fromTestId, toTestId, fromDate, toDate, entityType, fromKey, toKey) => {
  cy.get(`[data-testid="${fromTestId}"]`).type(fromDate);
  cy.get(`[data-testid="${toTestId}"]`).type(toDate);

  cy.wait('@getEntities').then((interception) => {
    const url = new URL(interception.request.url);
    const entityFilters = url.searchParams.get('entity_type_filters');
    const parsed = JSON.parse(entityFilters);
    
    expect(parsed[entityType][fromKey]).to.equal(fromDate);
    expect(parsed[entityType][toKey]).to.equal(toDate);
  });
});

// Command to test multi-select filters
Cypress.Commands.add('testMultiSelectFilter', (filterTestId, values, entityType, filterKey) => {
  cy.get(`[data-testid="${filterTestId}"]`).within(() => {
    values.forEach(value => {
      cy.get(`input[value="${value}"]`).check();
    });
  });

  cy.wait('@getEntities').then((interception) => {
    const url = new URL(interception.request.url);
    const entityFilters = url.searchParams.get('entity_type_filters');
    const parsed = JSON.parse(entityFilters);
    
    expect(parsed[entityType][filterKey]).to.include.members(values);
  });
});

// Command to test boolean filters
Cypress.Commands.add('testBooleanFilter', (filterTestId, entityType, filterKey) => {
  cy.get(`[data-testid="${filterTestId}"]`).check();

  cy.wait('@getEntities').then((interception) => {
    const url = new URL(interception.request.url);
    const entityFilters = url.searchParams.get('entity_type_filters');
    const parsed = JSON.parse(entityFilters);
    
    expect(parsed[entityType][filterKey]).to.be.true;
  });
});

// Command to setup test environment
Cypress.Commands.add('setupFilterTest', () => {
  cy.visit('/browse');
  cy.get('[data-testid="browse-page"]', { timeout: 10000 }).should('be.visible');
  cy.intercept('GET', '**/entities**').as('getEntities');
});

// Command to verify no API errors
Cypress.Commands.add('verifyNoApiErrors', () => {
  cy.wait('@getEntities').then((interception) => {
    expect(interception.response.statusCode).to.be.oneOf([200, 304]);
  });
});

// Command to test filter combination
Cypress.Commands.add('testFilterCombination', (filters) => {
  filters.forEach(filter => {
    const { entityType, filterTestId, value, expectedBackendKey } = filter;
    
    if (!expectedBackendKey) {
      cy.selectEntityType(entityType);
    }
    
    cy.applyEntityFilter(entityType, filterTestId, value, expectedBackendKey);
  });

  // Verify all filters are applied correctly
  cy.wait('@getEntities').then((interception) => {
    const url = new URL(interception.request.url);
    const entityFilters = url.searchParams.get('entity_type_filters');
    const parsed = JSON.parse(entityFilters);
    
    filters.forEach(filter => {
      const { entityType, filterKey, value, expectedBackendKey } = filter;
      const backendKey = expectedBackendKey || entityType.toLowerCase();
      
      expect(parsed).to.have.property(backendKey);
      
      if (Array.isArray(value)) {
        expect(parsed[backendKey][filterKey]).to.include.members(value);
      } else {
        expect(parsed[backendKey][filterKey]).to.equal(value);
      }
    });
  });
});

// Type definitions removed - use JSDoc comments for IDE support
