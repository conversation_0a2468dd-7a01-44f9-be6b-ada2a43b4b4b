describe('Infinite Scroll Functionality', () => {
  beforeEach(() => {
    // Visit the browse page before each test
    cy.visit('/browse');

    // Wait for the page to load completely
    cy.get('body').should('be.visible');

    // Wait for either resource cards to load or error message
    cy.get('body').then(($body) => {
      // Check if there's an error message first
      if ($body.find(':contains("Error:")').length > 0) {
        cy.log('⚠️ API Error detected on page load');
        cy.get(':contains("Error:")').should('be.visible');
      } else {
        // Wait for resource cards to load
        cy.get('[data-testid="resource-card"]', { timeout: 15000 })
          .should('have.length.greaterThan', 0);
      }
    });
  });

  it('should load more entities when scrolling to bottom', () => {
    // Skip test if there's an API error
    cy.get('body').then(($body) => {
      if ($body.find(':contains("Error:")').length > 0) {
        cy.log('⚠️ Skipping test due to API error');
        return;
      }

      // Get initial count of entities
      cy.get('[data-testid="resource-card"]')
        .its('length')
        .then((initialCount) => {
          cy.log(`Initial entity count: ${initialCount}`);

          if (initialCount === 0) {
            cy.log('⚠️ No entities loaded, skipping infinite scroll test');
            return;
          }

          // Scroll to bottom to trigger infinite scroll
          cy.scrollTo('bottom');

          // Wait a moment for intersection observer to trigger
          cy.wait(2000);

          // Check if loading indicator appears or if Load More button is available
          cy.get('body').then(($body) => {
            if ($body.find(':contains("Loading more resources")').length > 0) {
              cy.contains('Loading more resources').should('be.visible');

              // Wait for loading to complete and check if more entities are loaded
              cy.get('[data-testid="resource-card"]', { timeout: 15000 })
                .should('have.length.greaterThan', initialCount);

              cy.log('✅ Infinite scroll triggered successfully');
            } else if ($body.find(':contains("Load More Resources")').length > 0) {
              cy.log('ℹ️ Load More button available, testing manual trigger');
              cy.contains('Load More Resources').click();

              cy.get('[data-testid="resource-card"]', { timeout: 15000 })
                .should('have.length.greaterThan', initialCount);

              cy.log('✅ Manual load more working');
            } else {
              cy.log('ℹ️ No more pages available or all content loaded');
            }
          });
        });
    });
  });

  it('should load more entities when clicking Load More button', () => {
    // Get initial count of entities
    cy.get('[data-testid="resource-card"], .group.relative.bg-white')
      .its('length')
      .then((initialCount) => {
        cy.log(`Initial entity count: ${initialCount}`);
        
        // Find and click the Load More button
        cy.contains('Load More Resources', { timeout: 10000 })
          .should('be.visible')
          .click();
        
        // Wait for loading to complete
        cy.contains('Loading more resources', { timeout: 10000 }).should('be.visible');
        
        // Check if more entities are loaded
        cy.get('[data-testid="resource-card"], .group.relative.bg-white', { timeout: 15000 })
          .should('have.length.greaterThan', initialCount);
        
        cy.log('✅ Manual Load More button working');
      });
  });

  it('should work with filters applied', () => {
    // Apply a filter first
    cy.get('input[type="checkbox"]').first().check();
    
    // Wait for filtered results
    cy.wait(2000);
    
    // Get count after filtering
    cy.get('[data-testid="resource-card"], .group.relative.bg-white')
      .its('length')
      .then((filteredCount) => {
        cy.log(`Filtered entity count: ${filteredCount}`);
        
        // Scroll to bottom
        cy.scrollTo('bottom');
        
        // Check if Load More button or loading indicator appears
        cy.get('body').then(($body) => {
          if ($body.find(':contains("Load More Resources")').length > 0) {
            cy.contains('Load More Resources').click();
            
            // Wait for more entities to load
            cy.get('[data-testid="resource-card"], .group.relative.bg-white', { timeout: 15000 })
              .should('have.length.greaterThan', filteredCount);
            
            cy.log('✅ Infinite scroll with filters working');
          } else {
            cy.log('ℹ️ No more pages available with current filter');
          }
        });
      });
  });

  it('should work with search terms', () => {
    // Enter search term
    cy.get('input[type="search"], input[placeholder*="search"], input[placeholder*="Search"]')
      .type('AI{enter}');
    
    // Wait for search results
    cy.wait(3000);
    
    // Get count after search
    cy.get('[data-testid="resource-card"], .group.relative.bg-white')
      .its('length')
      .then((searchCount) => {
        cy.log(`Search result count: ${searchCount}`);
        
        // Scroll to bottom
        cy.scrollTo('bottom');
        
        // Check if Load More button or loading indicator appears
        cy.get('body').then(($body) => {
          if ($body.find(':contains("Load More Resources")').length > 0) {
            cy.contains('Load More Resources').click();
            
            // Wait for more entities to load
            cy.get('[data-testid="resource-card"], .group.relative.bg-white', { timeout: 15000 })
              .should('have.length.greaterThan', searchCount);
            
            cy.log('✅ Infinite scroll with search working');
          } else {
            cy.log('ℹ️ No more pages available with current search');
          }
        });
      });
  });

  it('should show proper loading states', () => {
    // Scroll to bottom
    cy.scrollTo('bottom');
    
    // Check for loading indicator
    cy.get('body').then(($body) => {
      if ($body.find(':contains("Loading more resources")').length > 0) {
        cy.contains('Loading more resources').should('be.visible');
        cy.log('✅ Loading state displayed correctly');
      } else if ($body.find(':contains("All resources loaded")').length > 0) {
        cy.contains('All resources loaded').should('be.visible');
        cy.log('✅ End state displayed correctly');
      }
    });
  });

  it('should display pagination information correctly', () => {
    // Check for pagination info
    cy.get('body').should('contain.text', 'resources found');
    
    // Check for Load More button with page info
    cy.get('body').then(($body) => {
      if ($body.find(':contains("Load More Resources")').length > 0) {
        cy.contains('Load More Resources').should('contain.text', 'of');
        cy.log('✅ Pagination information displayed correctly');
      }
    });
  });

  it('should handle multiple scroll operations', () => {
    let previousCount = 0;
    
    // Function to perform scroll and load more
    const scrollAndLoad = (iteration) => {
      cy.get('[data-testid="resource-card"], .group.relative.bg-white')
        .its('length')
        .then((currentCount) => {
          cy.log(`Iteration ${iteration}: Current count = ${currentCount}`);
          
          if (currentCount > previousCount || iteration === 1) {
            previousCount = currentCount;
            
            // Scroll to bottom
            cy.scrollTo('bottom');
            
            // Wait a bit
            cy.wait(2000);
            
            // Check if we can load more
            cy.get('body').then(($body) => {
              if ($body.find(':contains("Load More Resources")').length > 0) {
                cy.contains('Load More Resources').click();
                cy.wait(3000);
                
                if (iteration < 3) {
                  scrollAndLoad(iteration + 1);
                }
              } else {
                cy.log(`✅ Completed ${iteration} scroll operations`);
              }
            });
          }
        });
    };
    
    scrollAndLoad(1);
  });

  it('should not break when reaching the end of results', () => {
    // Keep scrolling until we reach the end
    const scrollUntilEnd = (attempts = 0) => {
      if (attempts > 10) {
        cy.log('✅ Reached maximum attempts or end of results');
        return;
      }
      
      cy.scrollTo('bottom');
      cy.wait(2000);
      
      cy.get('body').then(($body) => {
        if ($body.find(':contains("Load More Resources")').length > 0) {
          cy.contains('Load More Resources').click();
          cy.wait(3000);
          scrollUntilEnd(attempts + 1);
        } else if ($body.find(':contains("All resources loaded")').length > 0) {
          cy.contains('All resources loaded').should('be.visible');
          cy.log('✅ Properly reached end of results');
        } else {
          cy.log('✅ No more content to load');
        }
      });
    };
    
    scrollUntilEnd();
  });
});
