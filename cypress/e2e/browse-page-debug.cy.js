describe('Browse Page Debug', () => {
  it('should load the browse page and show what content is available', () => {
    cy.visit('/browse');
    
    // Wait for page to load
    cy.get('body').should('be.visible');
    
    // Take a screenshot to see what's on the page
    cy.screenshot('browse-page-loaded');
    
    // Log the page title
    cy.title().then((title) => {
      cy.log(`Page title: ${title}`);
    });
    
    // Check for various elements that might be on the page
    cy.get('body').then(($body) => {
      const bodyText = $body.text();
      cy.log(`Page contains text: ${bodyText.substring(0, 500)}...`);
      
      // Check for error messages
      if (bodyText.includes('Error:') || bodyText.includes('Failed to')) {
        cy.log('❌ Error detected on page');
        cy.get('*:contains("Error"), *:contains("Failed")').each(($el) => {
          cy.log(`Error element: ${$el.text()}`);
        });
      }
      
      // Check for loading states
      if (bodyText.includes('Loading')) {
        cy.log('⏳ Loading state detected');
        cy.get('*:contains("Loading")').each(($el) => {
          cy.log(`Loading element: ${$el.text()}`);
        });
      }
      
      // Check for resource cards
      const resourceCards = $body.find('[data-testid="resource-card"]');
      cy.log(`Found ${resourceCards.length} resource cards with data-testid`);
      
      const groupCards = $body.find('.group.relative.bg-white');
      cy.log(`Found ${groupCards.length} cards with group.relative.bg-white class`);
      
      // Check for any cards or grid items
      const anyCards = $body.find('[class*="card"], [class*="grid"], [class*="item"]');
      cy.log(`Found ${anyCards.length} potential card elements`);
      
      // Check for buttons
      const buttons = $body.find('button');
      cy.log(`Found ${buttons.length} buttons on page`);
      
      // Check for Load More button specifically
      const loadMoreButtons = $body.find('*:contains("Load More")');
      cy.log(`Found ${loadMoreButtons.length} Load More buttons`);
      
      // Check for pagination info
      const paginationInfo = $body.find('*:contains("resources found")');
      cy.log(`Found ${paginationInfo.length} pagination info elements`);
      
      // Log all main content areas
      const mainContent = $body.find('main, [role="main"], .main-content');
      cy.log(`Found ${mainContent.length} main content areas`);
      
      // Check for specific browse page elements
      const searchInputs = $body.find('input[type="search"], input[placeholder*="search"]');
      cy.log(`Found ${searchInputs.length} search inputs`);
      
      const filters = $body.find('*:contains("Filter"), input[type="checkbox"]');
      cy.log(`Found ${filters.length} filter elements`);
    });
    
    // Wait a bit longer to see if content loads
    cy.wait(5000);
    
    // Take another screenshot after waiting
    cy.screenshot('browse-page-after-wait');
    
    // Check again for resource cards
    cy.get('body').then(($body) => {
      const resourceCards = $body.find('[data-testid="resource-card"]');
      cy.log(`After waiting: Found ${resourceCards.length} resource cards`);
      
      if (resourceCards.length > 0) {
        cy.log('✅ Resource cards found after waiting');
        cy.get('[data-testid="resource-card"]').should('have.length.greaterThan', 0);
      } else {
        cy.log('❌ Still no resource cards found');
        
        // Check if there's an API error or empty state
        const errorElements = $body.find('*:contains("Error"), *:contains("Failed"), *:contains("No resources")');
        if (errorElements.length > 0) {
          cy.log('Found error or empty state elements:');
          errorElements.each((index, el) => {
            cy.log(`- ${Cypress.$(el).text()}`);
          });
        }
      }
    });
  });
  
  it('should test infinite scroll if resources are available', () => {
    cy.visit('/browse');
    cy.wait(5000);
    
    cy.get('body').then(($body) => {
      const resourceCards = $body.find('[data-testid="resource-card"]');
      
      if (resourceCards.length > 0) {
        cy.log(`✅ Found ${resourceCards.length} resource cards, testing infinite scroll`);
        
        const initialCount = resourceCards.length;
        
        // Scroll to bottom
        cy.scrollTo('bottom');
        cy.wait(3000);
        
        // Check if more cards loaded or if Load More button appeared
        cy.get('body').then(($bodyAfterScroll) => {
          const newResourceCards = $bodyAfterScroll.find('[data-testid="resource-card"]');
          const loadMoreButton = $bodyAfterScroll.find('*:contains("Load More Resources")');
          
          if (newResourceCards.length > initialCount) {
            cy.log('✅ Infinite scroll worked - more cards loaded automatically');
          } else if (loadMoreButton.length > 0) {
            cy.log('✅ Load More button appeared, clicking it');
            cy.contains('Load More Resources').click();
            cy.wait(3000);
            
            cy.get('[data-testid="resource-card"]').should('have.length.greaterThan', initialCount);
            cy.log('✅ Manual load more worked');
          } else {
            cy.log('ℹ️ No more content to load or infinite scroll not triggered');
          }
        });
      } else {
        cy.log('⚠️ No resource cards found, skipping infinite scroll test');
      }
    });
  });
});
