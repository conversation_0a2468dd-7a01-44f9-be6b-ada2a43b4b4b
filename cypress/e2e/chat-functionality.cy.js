describe('Chat Functionality', () => {
  beforeEach(() => {
    // Clear localStorage to ensure clean state
    cy.clearLocalStorage();
    
    // Visit the chat page
    cy.visit('/chat');
    
    // Wait for page to load
    cy.wait(1000);
  });

  describe('Welcome Screen Display', () => {
    it('should show welcome screen on initial page load', () => {
      // Check that welcome screen is visible
      cy.contains('Welcome to AI Navigator').should('be.visible');
      cy.contains("I'm your intelligent AI assistant").should('be.visible');
      cy.contains('Try asking me about...').should('be.visible');
      
      // Check that suggestion cards are present
      cy.get('[role="button"]').should('have.length.at.least', 3);
      cy.contains('Find AI Tools').should('be.visible');
      cy.contains('Coding Assistant').should('be.visible');
      cy.contains('Learning Resources').should('be.visible');
    });

    it('should not show any chat messages initially', () => {
      // Ensure no chat messages are visible initially
      cy.get('[role="log"]').within(() => {
        // Should only contain welcome screen, no chat messages
        cy.get('.mb-8').should('not.exist'); // Chat message containers
      });
    });
  });

  describe('Authentication Flow', () => {
    it('should show login prompt for unauthenticated users when trying to chat', () => {
      // Try to type in chat input
      cy.get('textarea[placeholder*="Ask me about"]').should('be.disabled');
      
      // Should show login prompt or message
      cy.contains('log in', { matchCase: false }).should('be.visible');
    });
  });

  describe('Chat Interaction (Authenticated)', () => {
    beforeEach(() => {
      // Mock authentication
      cy.window().then((win) => {
        win.localStorage.setItem('auth_session', JSON.stringify({
          access_token: 'mock-token',
          user: { id: 'test-user', email: '<EMAIL>' }
        }));
      });
      
      // Reload to apply auth state
      cy.reload();
      cy.wait(1000);
    });

    it('should hide welcome screen after sending first message', () => {
      // Verify welcome screen is initially visible
      cy.contains('Welcome to AI Navigator').should('be.visible');

      // Mock the chat API response
      cy.intercept('POST', '**/chat', {
        statusCode: 200,
        body: {
          message: 'Here are some great AI tools for content creation...',
          session_id: 'test-session-123',
          discovered_entities: [],
          conversation_stage: 'discovery',
          should_transition_to_recommendations: false,
          metadata: {
            response_time: 1000,
            llm_provider: 'test'
          },
          generated_at: new Date().toISOString()
        }
      }).as('chatRequest');

      // Type a message
      const testMessage = 'What are the best AI tools for content creation?';
      cy.get('textarea[placeholder*="Ask me about"]').type(testMessage);

      // Send the message
      cy.get('button[type="submit"]').click();

      // Immediately check that welcome screen is hidden (should happen before API response)
      cy.contains('Welcome to AI Navigator').should('not.exist');

      // Wait for API call
      cy.wait('@chatRequest');

      // Wait for UI update
      cy.wait(1000);

      // Verify welcome screen is still hidden
      cy.contains('Welcome to AI Navigator').should('not.exist');

      // Verify chat messages are visible
      cy.contains('Hello! I\'m your AI Navigator').should('be.visible'); // Welcome message
      cy.contains(testMessage).should('be.visible'); // User message
      cy.contains('Here are some great AI tools').should('be.visible'); // AI response
    });

    it('should handle suggestion clicks correctly', () => {
      // Verify welcome screen is initially visible
      cy.contains('Welcome to AI Navigator').should('be.visible');

      // Mock the chat API response
      cy.intercept('POST', '**/chat', {
        statusCode: 200,
        body: {
          message: 'Here are some excellent coding AI tools...',
          session_id: 'test-session-456',
          discovered_entities: [],
          conversation_stage: 'discovery',
          should_transition_to_recommendations: false,
          metadata: {
            response_time: 1000,
            llm_provider: 'test'
          },
          generated_at: new Date().toISOString()
        }
      }).as('chatRequest');

      // Click on a suggestion card
      cy.contains('Coding Assistant').click();

      // Immediately check that welcome screen is hidden
      cy.contains('Welcome to AI Navigator').should('not.exist');

      // Wait for API call
      cy.wait('@chatRequest');

      // Wait for UI update
      cy.wait(1000);

      // Verify welcome screen is still hidden
      cy.contains('Welcome to AI Navigator').should('not.exist');

      // Verify chat messages are visible
      cy.contains('Hello! I\'m your AI Navigator').should('be.visible'); // Welcome message
      cy.contains('I need help with coding').should('be.visible'); // Suggestion text
      cy.contains('Here are some excellent coding').should('be.visible'); // AI response
    });

    it('should not show welcome screen again after subsequent messages', () => {
      // Send first message
      cy.intercept('POST', '**/chat', {
        statusCode: 200,
        body: {
          message: 'Great question! Here are some options...',
          session_id: 'test-session-789',
          discovered_entities: []
        }
      }).as('chatRequest');
      
      cy.get('textarea[placeholder*="Ask me about"]').type('First message');
      cy.get('button[type="submit"]').click();
      cy.wait('@chatRequest');
      cy.wait(1000);
      
      // Send second message
      cy.intercept('POST', '**/chat', {
        statusCode: 200,
        body: {
          message: 'Here\'s more information...',
          session_id: 'test-session-789',
          discovered_entities: []
        }
      }).as('chatRequest2');
      
      cy.get('textarea[placeholder*="Ask me about"]').type('Second message');
      cy.get('button[type="submit"]').click();
      cy.wait('@chatRequest2');
      cy.wait(1000);
      
      // Verify welcome screen is still hidden
      cy.contains('Welcome to AI Navigator').should('not.exist');
      
      // Verify both messages are visible
      cy.contains('First message').should('be.visible');
      cy.contains('Second message').should('be.visible');
    });
  });

  describe('New Conversation', () => {
    beforeEach(() => {
      // Mock authentication
      cy.window().then((win) => {
        win.localStorage.setItem('auth_session', JSON.stringify({
          access_token: 'mock-token',
          user: { id: 'test-user', email: '<EMAIL>' }
        }));
      });
      
      cy.reload();
      cy.wait(1000);
    });

    it('should show welcome screen again when starting new conversation', () => {
      // Send a message first
      cy.intercept('POST', '**/chat', {
        statusCode: 200,
        body: {
          message: 'Response to first message',
          session_id: 'test-session-clear',
          discovered_entities: []
        }
      }).as('chatRequest');
      
      cy.get('textarea[placeholder*="Ask me about"]').type('Test message');
      cy.get('button[type="submit"]').click();
      cy.wait('@chatRequest');
      cy.wait(1000);
      
      // Verify welcome screen is hidden
      cy.contains('Welcome to AI Navigator').should('not.exist');
      
      // Click new conversation button (if available)
      cy.get('button').contains('New', { matchCase: false }).click();
      
      // Verify welcome screen is visible again
      cy.contains('Welcome to AI Navigator').should('be.visible');
      cy.contains("I'm your intelligent AI assistant").should('be.visible');
    });
  });
});
