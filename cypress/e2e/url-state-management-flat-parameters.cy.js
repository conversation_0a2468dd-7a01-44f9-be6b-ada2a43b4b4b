describe('URL State Management - Flat Parameters', () => {
  beforeEach(() => {
    cy.visit('/browse');
    cy.wait(2000); // Allow page to load
  });

  describe('URL Parameter Encoding', () => {
    it('should encode single flat parameters in URL', () => {
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.get('[data-testid="has-api-filter"]').click();
      
      cy.url().should('include', 'has_api=true');
      cy.url().should('not.include', 'entity_type_filters');
    });

    it('should encode array parameters correctly', () => {
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.get('[data-testid="technical-levels-filter"]').contains('BEGINNER').click();
      cy.get('[data-testid="technical-levels-filter"]').contains('INTERMEDIATE').click();
      
      cy.url().should('include', 'technical_levels=BEGINNER');
      cy.url().should('include', 'technical_levels=INTERMEDIATE');
    });

    it('should encode special characters in search parameters', () => {
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.get('[data-testid="key-features-search-filter"]').type('natural language processing & AI');
      
      cy.url().should('include', 'key_features_search=natural%20language%20processing%20%26%20AI');
    });

    it('should handle boolean parameters correctly', () => {
      cy.get('[data-testid="entity-type-filter"]').contains('Course').click();
      cy.get('[data-testid="certificate-available-filter"]').click();
      
      cy.url().should('include', 'certificate_available=true');
      
      // Uncheck and verify it's removed
      cy.get('[data-testid="certificate-available-filter"]').click();
      cy.url().should('not.include', 'certificate_available');
    });

    it('should handle number parameters correctly', () => {
      cy.get('[data-testid="entity-type-filter"]').contains('Job').click();
      cy.get('[data-testid="salary-min-filter"]').type('100');
      cy.get('[data-testid="salary-max-filter"]').type('200');
      
      cy.url().should('include', 'salary_min=100');
      cy.url().should('include', 'salary_max=200');
    });
  });

  describe('URL Parameter Decoding', () => {
    it('should restore single boolean parameters from URL', () => {
      cy.visit('/browse?has_api=true');
      cy.wait(1000);
      
      cy.get('[data-testid="has-api-filter"]').should('be.checked');
    });

    it('should restore array parameters from URL', () => {
      cy.visit('/browse?technical_levels=BEGINNER&technical_levels=INTERMEDIATE');
      cy.wait(1000);
      
      cy.get('[data-testid="technical-levels-filter"]').within(() => {
        cy.contains('BEGINNER').should('be.checked');
        cy.contains('INTERMEDIATE').should('be.checked');
      });
    });

    it('should restore string parameters from URL', () => {
      cy.visit('/browse?instructor_name=Andrew%20Ng&company_name=Google');
      cy.wait(1000);
      
      cy.get('[data-testid="instructor-name-filter"]').should('have.value', 'Andrew Ng');
      cy.get('[data-testid="company-name-filter"]').should('have.value', 'Google');
    });

    it('should restore number parameters from URL', () => {
      cy.visit('/browse?salary_min=100&salary_max=200&enrollment_min=1000');
      cy.wait(1000);
      
      cy.get('[data-testid="salary-min-filter"]').should('have.value', '100');
      cy.get('[data-testid="salary-max-filter"]').should('have.value', '200');
      cy.get('[data-testid="enrollment-min-filter"]').should('have.value', '1000');
    });

    it('should restore complex filter combinations from URL', () => {
      const url = '/browse?has_api=true&technical_levels=BEGINNER&technical_levels=ADVANCED&certificate_available=true&salary_min=100&is_online=true';
      cy.visit(url);
      cy.wait(1000);
      
      cy.get('[data-testid="has-api-filter"]').should('be.checked');
      cy.get('[data-testid="certificate-available-filter"]').should('be.checked');
      cy.get('[data-testid="is-online-filter"]').should('be.checked');
      cy.get('[data-testid="salary-min-filter"]').should('have.value', '100');
    });
  });

  describe('URL State Persistence', () => {
    it('should maintain filter state on page refresh', () => {
      // Apply multiple filters
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.get('[data-testid="has-api-filter"]').click();
      cy.get('[data-testid="technical-levels-filter"]').contains('BEGINNER').click();
      cy.get('[data-testid="open-source-filter"]').click();
      
      // Capture current URL
      cy.url().then((currentUrl) => {
        // Refresh page
        cy.reload();
        cy.wait(2000);
        
        // Verify URL is the same
        cy.url().should('eq', currentUrl);
        
        // Verify filters are still applied
        cy.get('[data-testid="has-api-filter"]').should('be.checked');
        cy.get('[data-testid="open-source-filter"]').should('be.checked');
        cy.get('[data-testid="technical-levels-filter"]').within(() => {
          cy.contains('BEGINNER').should('be.checked');
        });
      });
    });

    it('should maintain filter state on browser back/forward navigation', () => {
      // Apply initial filters
      cy.get('[data-testid="entity-type-filter"]').contains('Course').click();
      cy.get('[data-testid="certificate-available-filter"]').click();
      
      // Navigate to different page
      cy.visit('/');
      
      // Go back
      cy.go('back');
      cy.wait(1000);
      
      // Verify filters are restored
      cy.url().should('include', 'certificate_available=true');
      cy.get('[data-testid="certificate-available-filter"]').should('be.checked');
    });

    it('should handle URL sharing and bookmarking', () => {
      // Create a complex filter state
      cy.get('[data-testid="entity-type-filter"]').contains('Job').click();
      cy.get('[data-testid="employment-types-filter"]').contains('Full-time').click();
      cy.get('[data-testid="location-types-filter"]').contains('Remote').click();
      cy.get('[data-testid="salary-min-filter"]').type('100');
      
      // Capture the URL
      cy.url().then((filterUrl) => {
        // Navigate away
        cy.visit('/');
        
        // Navigate back using the captured URL
        cy.visit(filterUrl);
        cy.wait(1000);
        
        // Verify all filters are restored
        cy.get('[data-testid="employment-types-filter"]').within(() => {
          cy.contains('Full-time').should('be.checked');
        });
        cy.get('[data-testid="location-types-filter"]').within(() => {
          cy.contains('Remote').should('be.checked');
        });
        cy.get('[data-testid="salary-min-filter"]').should('have.value', '100');
      });
    });
  });

  describe('URL Parameter Cleanup', () => {
    it('should remove parameters when filters are cleared', () => {
      // Apply filters
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.get('[data-testid="has-api-filter"]').click();
      cy.get('[data-testid="technical-levels-filter"]').contains('BEGINNER').click();
      
      // Verify parameters are in URL
      cy.url().should('include', 'has_api=true');
      cy.url().should('include', 'technical_levels=BEGINNER');
      
      // Clear filters
      cy.get('[data-testid="clear-all-filters"]').click();
      
      // Verify parameters are removed
      cy.url().should('not.include', 'has_api');
      cy.url().should('not.include', 'technical_levels');
    });

    it('should remove individual parameters when filters are unchecked', () => {
      cy.get('[data-testid="entity-type-filter"]').contains('Course').click();
      cy.get('[data-testid="certificate-available-filter"]').click();
      cy.get('[data-testid="has-syllabus-filter"]').click();
      
      cy.url().should('include', 'certificate_available=true');
      cy.url().should('include', 'has_syllabus=true');
      
      // Uncheck one filter
      cy.get('[data-testid="certificate-available-filter"]').click();
      
      cy.url().should('not.include', 'certificate_available');
      cy.url().should('include', 'has_syllabus=true');
    });

    it('should handle empty array parameters', () => {
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.get('[data-testid="technical-levels-filter"]').contains('BEGINNER').click();
      cy.get('[data-testid="technical-levels-filter"]').contains('INTERMEDIATE').click();
      
      cy.url().should('include', 'technical_levels=BEGINNER');
      cy.url().should('include', 'technical_levels=INTERMEDIATE');
      
      // Uncheck all technical levels
      cy.get('[data-testid="technical-levels-filter"]').contains('BEGINNER').click();
      cy.get('[data-testid="technical-levels-filter"]').contains('INTERMEDIATE').click();
      
      cy.url().should('not.include', 'technical_levels');
    });
  });

  describe('URL Parameter Validation', () => {
    it('should handle invalid parameter values gracefully', () => {
      cy.visit('/browse?has_api=invalid&technical_levels=INVALID_LEVEL&salary_min=not_a_number');
      cy.wait(1000);
      
      // Page should still load without errors
      cy.get('[data-testid="browse-page"]').should('be.visible');
      
      // Invalid values should be ignored or handled gracefully
      cy.get('[data-testid="has-api-filter"]').should('not.be.checked');
    });

    it('should handle malformed URL parameters', () => {
      cy.visit('/browse?technical_levels=%invalid%&certificate_available=');
      cy.wait(1000);
      
      cy.get('[data-testid="browse-page"]').should('be.visible');
      cy.get('[data-testid="certificate-available-filter"]').should('not.be.checked');
    });

    it('should handle very long parameter values', () => {
      const longValue = 'a'.repeat(1000);
      cy.visit(`/browse?key_features_search=${encodeURIComponent(longValue)}`);
      cy.wait(1000);
      
      cy.get('[data-testid="browse-page"]').should('be.visible');
    });
  });

  describe('Cross-Entity URL State', () => {
    it('should maintain filters when switching between entity types', () => {
      // Apply tool-specific filter
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.get('[data-testid="has-api-filter"]').click();
      
      // Add course-specific filter
      cy.get('[data-testid="entity-type-filter"]').contains('Course').click();
      cy.get('[data-testid="certificate-available-filter"]').click();
      
      // Both filters should be in URL
      cy.url().should('include', 'has_api=true');
      cy.url().should('include', 'certificate_available=true');
      
      // Refresh and verify both are restored
      cy.reload();
      cy.wait(1000);
      
      cy.get('[data-testid="has-api-filter"]').should('be.checked');
      cy.get('[data-testid="certificate-available-filter"]').should('be.checked');
    });

    it('should handle entity type deselection correctly', () => {
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.get('[data-testid="has-api-filter"]').click();
      cy.get('[data-testid="technical-levels-filter"]').contains('BEGINNER').click();
      
      // Deselect AI Tool entity type
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      
      // Tool-specific filters should remain in URL (for cross-entity compatibility)
      cy.url().should('include', 'has_api=true');
      cy.url().should('include', 'technical_levels=BEGINNER');
    });
  });

  describe('URL Length and Performance', () => {
    it('should handle URLs with many parameters efficiently', () => {
      // Apply many filters to create a long URL
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.get('[data-testid="has-api-filter"]').click();
      cy.get('[data-testid="has-free-tier-filter"]').click();
      cy.get('[data-testid="open-source-filter"]').click();
      cy.get('[data-testid="mobile-support-filter"]').click();
      cy.get('[data-testid="demo-available-filter"]').click();
      
      cy.get('[data-testid="technical-levels-filter"]').contains('BEGINNER').click();
      cy.get('[data-testid="technical-levels-filter"]').contains('INTERMEDIATE').click();
      cy.get('[data-testid="technical-levels-filter"]').contains('ADVANCED').click();
      
      cy.get('[data-testid="learning-curves-filter"]').contains('LOW').click();
      cy.get('[data-testid="learning-curves-filter"]').contains('MEDIUM').click();
      
      // URL should still work and page should load quickly
      cy.url().then((longUrl) => {
        const startTime = Date.now();
        cy.reload();
        cy.wait(1000);
        cy.get('[data-testid="browse-page"]').should('be.visible').then(() => {
          const loadTime = Date.now() - startTime;
          expect(loadTime).to.be.lessThan(5000); // Should load within 5 seconds
        });
      });
    });

    it('should maintain URL readability with flat parameters', () => {
      cy.get('[data-testid="entity-type-filter"]').contains('Course').click();
      cy.get('[data-testid="certificate-available-filter"]').click();
      cy.get('[data-testid="skill-levels-filter"]').contains('INTERMEDIATE').click();
      
      cy.url().then((url) => {
        // URL should be readable and not contain nested JSON
        expect(url).to.include('certificate_available=true');
        expect(url).to.include('skill_levels=INTERMEDIATE');
        expect(url).to.not.include('entity_type_filters');
        expect(url).to.not.include('%7B'); // No encoded JSON brackets
      });
    });
  });
});
