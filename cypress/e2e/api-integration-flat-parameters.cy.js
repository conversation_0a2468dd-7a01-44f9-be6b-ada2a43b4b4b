describe('API Integration - Flat Parameter Testing', () => {
  const API_BASE_URL = Cypress.env('API_BASE_URL') || 'https://ai-nav.onrender.com';

  describe('Basic Flat Parameter API Calls', () => {
    it('should make API calls with single flat parameters', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          has_api: 'true',
          limit: 5
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body).to.have.property('data');
        expect(response.body).to.have.property('meta');
        expect(Array.isArray(response.body.data)).to.be.true;
      });
    });

    it('should handle boolean parameters correctly', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          has_free_tier: 'true',
          open_source: 'true',
          certificate_available: 'true',
          is_online: 'true',
          limit: 5
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });

    it('should handle string parameters correctly', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          instructor_name: 'Andrew Ng',
          company_name: 'Google',
          author_name: 'Stuart Russell',
          current_version: '2.0',
          limit: 5
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });

    it('should handle number parameters correctly', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          salary_min: 100,
          salary_max: 200,
          enrollment_min: 1000,
          enrollment_max: 50000,
          price_min: 500,
          price_max: 2000,
          limit: 5
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });

    it('should handle date parameters correctly', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          start_date_from: '2024-01-01',
          start_date_to: '2024-12-31',
          end_date_from: '2024-01-01',
          end_date_to: '2024-12-31',
          limit: 5
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });
  });

  describe('Array Parameter Handling', () => {
    it('should handle single array values', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          technical_levels: 'BEGINNER',
          skill_levels: 'INTERMEDIATE',
          employment_types: 'FULL_TIME',
          event_types: 'Conference',
          limit: 5
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });

    it('should handle multiple array values', () => {
      // Test multiple values for array parameters
      const params = new URLSearchParams();
      params.append('technical_levels', 'BEGINNER');
      params.append('technical_levels', 'INTERMEDIATE');
      params.append('skill_levels', 'BEGINNER');
      params.append('skill_levels', 'ADVANCED');
      params.append('employment_types', 'FULL_TIME');
      params.append('employment_types', 'PART_TIME');
      params.append('limit', '5');

      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities?${params.toString()}`
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });

    it('should handle comma-separated array values', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          technical_levels: 'BEGINNER,INTERMEDIATE,ADVANCED',
          event_types: 'Conference,Workshop,Webinar',
          services_offered: 'AI Strategy,Machine Learning,Data Science',
          limit: 5
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });
  });

  describe('Entity-Specific Filter Testing', () => {
    it('should filter AI Tools with tool-specific parameters', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          entity_types: 'tool',
          technical_levels: 'BEGINNER',
          has_api: 'true',
          has_free_tier: 'true',
          open_source: 'true',
          mobile_support: 'true',
          demo_available: 'true',
          learning_curves: 'LOW',
          limit: 10
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });

    it('should filter Courses with course-specific parameters', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          entity_types: 'course',
          skill_levels: 'INTERMEDIATE',
          certificate_available: 'true',
          instructor_name: 'Andrew Ng',
          duration_text: '10 hours',
          enrollment_min: 1000,
          enrollment_max: 50000,
          has_syllabus: 'true',
          limit: 10
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });

    it('should filter Jobs with job-specific parameters', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          entity_types: 'job',
          employment_types: 'FULL_TIME',
          experience_levels: 'SENIOR',
          location_types: 'Remote',
          company_name: 'Google',
          job_title: 'Machine Learning Engineer',
          salary_min: 100,
          salary_max: 200,
          has_application_url: 'true',
          limit: 10
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });

    it('should filter Events with event-specific parameters', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          entity_types: 'event',
          event_types: 'Conference',
          is_online: 'true',
          start_date_from: '2024-01-01',
          start_date_to: '2024-12-31',
          location: 'San Francisco',
          price_text: 'Free',
          has_registration_url: 'true',
          speakers_search: 'Geoffrey Hinton',
          limit: 10
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });

    it('should filter Hardware with hardware-specific parameters', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          entity_types: 'hardware',
          price_range: '$1000-$5000',
          price_min: 1000,
          price_max: 5000,
          memory_search: '32GB',
          processor_search: 'Intel i9',
          limit: 10
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });

    it('should filter Agencies with agency-specific parameters', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          entity_types: 'agency',
          services_offered: 'AI Strategy',
          industry_focus: 'Healthcare',
          has_portfolio: 'true',
          limit: 10
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });

    it('should filter Software with software-specific parameters', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          entity_types: 'software',
          license_types: 'MIT',
          current_version: '2.0',
          limit: 10
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });

    it('should filter Books with book-specific parameters', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          entity_types: 'book',
          author_name: 'Stuart Russell',
          isbn: '***********',
          formats: 'eBook',
          limit: 10
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });
  });

  describe('Cross-Entity Filtering', () => {
    it('should handle filters that apply to multiple entity types', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          has_api: 'true',           // Applies to tools and software
          certificate_available: 'true',  // Applies to courses
          is_online: 'true',         // Applies to events
          has_portfolio: 'true',     // Applies to agencies
          limit: 20
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });

    it('should combine general and entity-specific filters', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          // General filters
          hasFreeTier: 'true',
          apiAccess: 'true',
          
          // Entity-specific filters
          technical_levels: 'BEGINNER',
          skill_levels: 'INTERMEDIATE',
          employment_types: 'FULL_TIME',
          event_types: 'Conference',
          
          limit: 20
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle invalid parameter values gracefully', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          technical_levels: 'INVALID_LEVEL',
          salary_min: 'not_a_number',
          has_api: 'maybe',
          limit: 5
        },
        failOnStatusCode: false
      }).then((response) => {
        // Should either return 400 with validation errors or filter out invalid values
        expect([200, 400]).to.include(response.status);
      });
    });

    it('should handle empty array parameters', () => {
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          technical_levels: '',
          skill_levels: '',
          employment_types: '',
          limit: 5
        }
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });

    it('should handle very large parameter lists', () => {
      const params = new URLSearchParams();
      
      // Add many array values
      ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'].forEach(level => {
        params.append('technical_levels', level);
        params.append('skill_levels', level);
      });
      
      ['FULL_TIME', 'PART_TIME', 'CONTRACT', 'FREELANCE'].forEach(type => {
        params.append('employment_types', type);
      });
      
      ['Conference', 'Workshop', 'Webinar', 'Meetup', 'Hackathon'].forEach(type => {
        params.append('event_types', type);
      });
      
      params.append('limit', '10');

      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities?${params.toString()}`
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data).to.be.an('array');
      });
    });
  });

  describe('Response Format Validation', () => {
    it('should return consistent response format for all filter combinations', () => {
      const testCases = [
        { has_api: 'true' },
        { technical_levels: 'BEGINNER', skill_levels: 'INTERMEDIATE' },
        { employment_types: 'FULL_TIME', salary_min: 100 },
        { event_types: 'Conference', is_online: 'true' },
        { services_offered: 'AI Strategy', has_portfolio: 'true' }
      ];

      testCases.forEach((testCase, index) => {
        cy.request({
          method: 'GET',
          url: `${API_BASE_URL}/entities`,
          qs: { ...testCase, limit: 5 }
        }).then((response) => {
          expect(response.status).to.eq(200);
          
          // Validate response structure
          expect(response.body).to.have.property('data');
          expect(response.body).to.have.property('meta');
          expect(response.body.data).to.be.an('array');
          expect(response.body.meta).to.have.property('total');
          expect(response.body.meta).to.have.property('page');
          expect(response.body.meta).to.have.property('limit');
          expect(response.body.meta).to.have.property('totalPages');
          
          // Validate data types
          expect(response.body.meta.total).to.be.a('number');
          expect(response.body.meta.page).to.be.a('number');
          expect(response.body.meta.limit).to.be.a('number');
          expect(response.body.meta.totalPages).to.be.a('number');
        });
      });
    });
  });

  describe('Performance Testing', () => {
    it('should handle complex filter combinations efficiently', () => {
      const startTime = Date.now();
      
      cy.request({
        method: 'GET',
        url: `${API_BASE_URL}/entities`,
        qs: {
          // Multiple entity-specific filters
          technical_levels: 'BEGINNER,INTERMEDIATE',
          skill_levels: 'BEGINNER,INTERMEDIATE,ADVANCED',
          employment_types: 'FULL_TIME,PART_TIME',
          event_types: 'Conference,Workshop',
          services_offered: 'AI Strategy,Machine Learning',
          license_types: 'MIT,Apache',
          
          // General filters
          hasFreeTier: 'true',
          apiAccess: 'true',
          
          // Search and pagination
          searchTerm: 'AI',
          limit: 20,
          page: 1
        }
      }).then((response) => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        expect(response.status).to.eq(200);
        expect(responseTime).to.be.lessThan(5000); // Should respond within 5 seconds
        expect(response.body.data).to.be.an('array');
      });
    });
  });
});
