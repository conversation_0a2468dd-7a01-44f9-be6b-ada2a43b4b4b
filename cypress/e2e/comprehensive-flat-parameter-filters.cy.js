describe('Comprehensive Entity Filters - Flat Parameter API', () => {
  beforeEach(() => {
    cy.visit('/browse');
    cy.wait(2000); // Allow page to load
  });

  describe('Tool/AI Tool Filters', () => {
    beforeEach(() => {
      // Select AI Tool entity type first
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.wait(1000);
    });

    it('should filter by technical levels using flat parameters', () => {
      cy.get('[data-testid="technical-levels-filter"]').should('be.visible');
      cy.get('[data-testid="technical-levels-filter"]').contains('BEGINNER').click();
      
      // Check URL contains flat parameter
      cy.url().should('include', 'technical_levels=BEGINNER');
      
      // Verify API call is made with flat parameters
      cy.intercept('GET', '/entities*').as('getEntities');
      cy.wait('@getEntities').then((interception) => {
        expect(interception.request.url).to.include('technical_levels=BEGINNER');
        expect(interception.request.url).to.not.include('entity_type_filters');
      });
    });

    it('should filter by multiple technical levels', () => {
      cy.get('[data-testid="technical-levels-filter"]').contains('BEGINNER').click();
      cy.get('[data-testid="technical-levels-filter"]').contains('INTERMEDIATE').click();
      
      cy.url().should('include', 'technical_levels=BEGINNER');
      cy.url().should('include', 'technical_levels=INTERMEDIATE');
    });

    it('should filter by API access', () => {
      cy.get('[data-testid="has-api-filter"]').click();
      cy.url().should('include', 'has_api=true');
    });

    it('should filter by free tier availability', () => {
      cy.get('[data-testid="has-free-tier-filter"]').click();
      cy.url().should('include', 'has_free_tier=true');
    });

    it('should filter by open source status', () => {
      cy.get('[data-testid="open-source-filter"]').click();
      cy.url().should('include', 'open_source=true');
    });

    it('should filter by mobile support', () => {
      cy.get('[data-testid="mobile-support-filter"]').click();
      cy.url().should('include', 'mobile_support=true');
    });

    it('should filter by demo availability', () => {
      cy.get('[data-testid="demo-available-filter"]').click();
      cy.url().should('include', 'demo_available=true');
    });

    it('should filter by learning curves', () => {
      cy.get('[data-testid="learning-curves-filter"]').contains('LOW').click();
      cy.url().should('include', 'learning_curves=LOW');
    });

    it('should search by key features', () => {
      cy.get('[data-testid="key-features-search-filter"]').type('natural language processing');
      cy.url().should('include', 'key_features_search=natural%20language%20processing');
    });

    it('should search by use cases', () => {
      cy.get('[data-testid="use-cases-search-filter"]').type('content generation');
      cy.url().should('include', 'use_cases_search=content%20generation');
    });
  });

  describe('Course Filters', () => {
    beforeEach(() => {
      cy.get('[data-testid="entity-type-filter"]').contains('Course').click();
      cy.wait(1000);
    });

    it('should filter by skill levels', () => {
      cy.get('[data-testid="skill-levels-filter"]').contains('BEGINNER').click();
      cy.url().should('include', 'skill_levels=BEGINNER');
    });

    it('should filter by certificate availability', () => {
      cy.get('[data-testid="certificate-available-filter"]').click();
      cy.url().should('include', 'certificate_available=true');
    });

    it('should filter by instructor name', () => {
      cy.get('[data-testid="instructor-name-filter"]').type('Andrew Ng');
      cy.url().should('include', 'instructor_name=Andrew%20Ng');
    });

    it('should filter by duration', () => {
      cy.get('[data-testid="duration-text-filter"]').type('10 hours');
      cy.url().should('include', 'duration_text=10%20hours');
    });

    it('should filter by enrollment numbers', () => {
      cy.get('[data-testid="enrollment-min-filter"]').type('1000');
      cy.get('[data-testid="enrollment-max-filter"]').type('50000');
      cy.url().should('include', 'enrollment_min=1000');
      cy.url().should('include', 'enrollment_max=50000');
    });

    it('should filter by prerequisites', () => {
      cy.get('[data-testid="prerequisites-filter"]').type('Basic programming knowledge');
      cy.url().should('include', 'prerequisites=Basic%20programming%20knowledge');
    });

    it('should filter by syllabus availability', () => {
      cy.get('[data-testid="has-syllabus-filter"]').click();
      cy.url().should('include', 'has_syllabus=true');
    });
  });

  describe('Job Filters', () => {
    beforeEach(() => {
      cy.get('[data-testid="entity-type-filter"]').contains('Job').click();
      cy.wait(1000);
    });

    it('should filter by employment types', () => {
      cy.get('[data-testid="employment-types-filter"]').contains('Full-time').click();
      cy.url().should('include', 'employment_types=Full-time');
    });

    it('should filter by experience levels', () => {
      cy.get('[data-testid="experience-levels-filter"]').contains('Senior').click();
      cy.url().should('include', 'experience_levels=Senior');
    });

    it('should filter by location types', () => {
      cy.get('[data-testid="location-types-filter"]').contains('Remote').click();
      cy.url().should('include', 'location_types=Remote');
    });

    it('should filter by company name', () => {
      cy.get('[data-testid="company-name-filter"]').type('Google');
      cy.url().should('include', 'company_name=Google');
    });

    it('should filter by job title', () => {
      cy.get('[data-testid="job-title-filter"]').type('Machine Learning Engineer');
      cy.url().should('include', 'job_title=Machine%20Learning%20Engineer');
    });

    it('should filter by salary range', () => {
      cy.get('[data-testid="salary-min-filter"]').type('100');
      cy.get('[data-testid="salary-max-filter"]').type('200');
      cy.url().should('include', 'salary_min=100');
      cy.url().should('include', 'salary_max=200');
    });

    it('should filter by application URL availability', () => {
      cy.get('[data-testid="has-application-url-filter"]').click();
      cy.url().should('include', 'has_application_url=true');
    });
  });

  describe('Event Filters', () => {
    beforeEach(() => {
      cy.get('[data-testid="entity-type-filter"]').contains('Event').click();
      cy.wait(1000);
    });

    it('should filter by event types', () => {
      cy.get('[data-testid="event-types-filter"]').contains('Conference').click();
      cy.url().should('include', 'event_types=Conference');
    });

    it('should filter by online status', () => {
      cy.get('[data-testid="is-online-filter"]').click();
      cy.url().should('include', 'is_online=true');
    });

    it('should filter by date ranges', () => {
      cy.get('[data-testid="start-date-from-filter"]').type('2024-01-01');
      cy.get('[data-testid="start-date-to-filter"]').type('2024-12-31');
      cy.url().should('include', 'start_date_from=2024-01-01');
      cy.url().should('include', 'start_date_to=2024-12-31');
    });

    it('should filter by location', () => {
      cy.get('[data-testid="location-filter"]').type('San Francisco');
      cy.url().should('include', 'location=San%20Francisco');
    });

    it('should filter by price text', () => {
      cy.get('[data-testid="price-text-filter"]').type('Free');
      cy.url().should('include', 'price_text=Free');
    });

    it('should filter by registration URL availability', () => {
      cy.get('[data-testid="has-registration-url-filter"]').click();
      cy.url().should('include', 'has_registration_url=true');
    });

    it('should search by speakers', () => {
      cy.get('[data-testid="speakers-search-filter"]').type('Geoffrey Hinton');
      cy.url().should('include', 'speakers_search=Geoffrey%20Hinton');
    });
  });

  describe('Hardware Filters', () => {
    beforeEach(() => {
      cy.get('[data-testid="entity-type-filter"]').contains('Hardware').click();
      cy.wait(1000);
    });

    it('should filter by price range', () => {
      cy.get('[data-testid="price-range-filter"]').type('$1000-$5000');
      cy.url().should('include', 'price_range=%241000-%245000');
    });

    it('should filter by price min/max', () => {
      cy.get('[data-testid="price-min-filter"]').type('1000');
      cy.get('[data-testid="price-max-filter"]').type('5000');
      cy.url().should('include', 'price_min=1000');
      cy.url().should('include', 'price_max=5000');
    });

    it('should search by memory specifications', () => {
      cy.get('[data-testid="memory-search-filter"]').type('32GB');
      cy.url().should('include', 'memory_search=32GB');
    });

    it('should search by processor specifications', () => {
      cy.get('[data-testid="processor-search-filter"]').type('Intel i9');
      cy.url().should('include', 'processor_search=Intel%20i9');
    });
  });

  describe('Agency Filters', () => {
    beforeEach(() => {
      cy.get('[data-testid="entity-type-filter"]').contains('Agency').click();
      cy.wait(1000);
    });

    it('should filter by services offered', () => {
      cy.get('[data-testid="services-offered-filter"]').contains('AI Strategy').click();
      cy.url().should('include', 'services_offered=AI%20Strategy');
    });

    it('should filter by industry focus', () => {
      cy.get('[data-testid="industry-focus-filter"]').contains('Healthcare').click();
      cy.url().should('include', 'industry_focus=Healthcare');
    });

    it('should filter by portfolio availability', () => {
      cy.get('[data-testid="has-portfolio-filter"]').click();
      cy.url().should('include', 'has_portfolio=true');
    });
  });

  describe('Software Filters', () => {
    beforeEach(() => {
      cy.get('[data-testid="entity-type-filter"]').contains('Software').click();
      cy.wait(1000);
    });

    it('should filter by license types', () => {
      cy.get('[data-testid="license-types-filter"]').contains('MIT').click();
      cy.url().should('include', 'license_types=MIT');
    });

    it('should filter by current version', () => {
      cy.get('[data-testid="current-version-filter"]').type('2.0');
      cy.url().should('include', 'current_version=2.0');
    });
  });

  describe('Book Filters', () => {
    beforeEach(() => {
      cy.get('[data-testid="entity-type-filter"]').contains('Book').click();
      cy.wait(1000);
    });

    it('should filter by author name', () => {
      cy.get('[data-testid="author-name-filter"]').type('Stuart Russell');
      cy.url().should('include', 'author_name=Stuart%20Russell');
    });

    it('should filter by ISBN', () => {
      cy.get('[data-testid="isbn-filter"]').type('978-0262039');
      cy.url().should('include', 'isbn=978-0262039');
    });

    it('should filter by formats', () => {
      cy.get('[data-testid="formats-filter"]').contains('eBook').click();
      cy.url().should('include', 'formats=eBook');
    });
  });

  describe('Cross-Entity Filter Combinations', () => {
    it('should handle multiple entity types with different filters', () => {
      // Select multiple entity types
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.get('[data-testid="entity-type-filter"]').contains('Course').click();
      
      // Apply tool-specific filter
      cy.get('[data-testid="has-api-filter"]').click();
      
      // Apply course-specific filter
      cy.get('[data-testid="certificate-available-filter"]').click();
      
      // Verify both filters are in URL
      cy.url().should('include', 'has_api=true');
      cy.url().should('include', 'certificate_available=true');
      
      // Verify API call includes both flat parameters
      cy.intercept('GET', '/entities*').as('getEntities');
      cy.wait('@getEntities').then((interception) => {
        expect(interception.request.url).to.include('has_api=true');
        expect(interception.request.url).to.include('certificate_available=true');
      });
    });

    it('should clear all entity-specific filters', () => {
      // Apply multiple filters
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.get('[data-testid="has-api-filter"]').click();
      cy.get('[data-testid="technical-levels-filter"]').contains('BEGINNER').click();
      
      // Clear all filters
      cy.get('[data-testid="clear-all-filters"]').click();
      
      // Verify URL is clean
      cy.url().should('not.include', 'has_api');
      cy.url().should('not.include', 'technical_levels');
    });
  });

  describe('API Response Validation', () => {
    it('should receive proper API responses with flat parameters', () => {
      cy.intercept('GET', '/entities*').as('getEntities');
      
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.get('[data-testid="has-api-filter"]').click();
      
      cy.wait('@getEntities').then((interception) => {
        // Verify request uses flat parameters
        expect(interception.request.url).to.include('has_api=true');
        expect(interception.request.url).to.not.include('entity_type_filters');
        
        // Verify response structure
        expect(interception.response.statusCode).to.eq(200);
        expect(interception.response.body).to.have.property('data');
        expect(interception.response.body).to.have.property('meta');
      });
    });
  });

  describe('Filter State Persistence', () => {
    it('should maintain filter state on page refresh', () => {
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.get('[data-testid="has-api-filter"]').click();
      cy.get('[data-testid="technical-levels-filter"]').contains('BEGINNER').click();
      
      // Refresh page
      cy.reload();
      
      // Verify filters are still applied
      cy.url().should('include', 'has_api=true');
      cy.url().should('include', 'technical_levels=BEGINNER');
      cy.get('[data-testid="has-api-filter"]').should('be.checked');
    });

    it('should maintain filter state on navigation', () => {
      cy.get('[data-testid="entity-type-filter"]').contains('Course').click();
      cy.get('[data-testid="certificate-available-filter"]').click();
      
      // Navigate away and back
      cy.visit('/');
      cy.visit('/browse');
      
      // Verify filters are restored from URL
      cy.url().should('include', 'certificate_available=true');
    });
  });
});
