describe('UI Improvements - Entity Filters', () => {
  beforeEach(() => {
    cy.visit('/browse');
    cy.wait(2000); // Allow page to load
  });

  describe('Smart Type-Specific Filters UI', () => {
    beforeEach(() => {
      // Select AI Tool entity type to show filters
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.wait(1000);
    });

    it('should display clean, consistent filter layout', () => {
      // Check that the Smart Type-Specific Filters section is visible
      cy.get('[data-testid="entity-specific-filters"]').should('be.visible');
      
      // Check the header is clean and informative
      cy.contains('Smart Type-Specific Filters').should('be.visible');
      cy.contains('Advanced filtering options tailored to each entity type').should('be.visible');
      
      // Check AI Tool filters section
      cy.get('[data-testid="ai-tool-filters"]').should('be.visible');
      cy.contains('AI Tool Filters').should('be.visible');
      cy.contains('filters available').should('be.visible');
    });

    it('should have consistent boolean filter styling', () => {
      // Check boolean filters have consistent styling
      cy.get('[data-testid="has-api-filter"]').should('be.visible');
      cy.get('[data-testid="has-free-tier-filter"]').should('be.visible');
      cy.get('[data-testid="open-source-filter"]').should('be.visible');
      
      // Check they have proper hover states and are clickable
      cy.get('[data-testid="has-api-filter"]').should('have.class', 'cursor-pointer');
    });

    it('should have reliable multiselect button clicks', () => {
      // Test technical levels multiselect
      cy.get('[data-testid="technical-levels-filter"]').should('be.visible');
      
      // Click BEGINNER option
      cy.get('[data-testid="technical-levels-filter"]').within(() => {
        cy.contains('BEGINNER').click();
      });
      
      // Verify it's selected (should appear in selected section)
      cy.get('[data-testid="technical-levels-filter"]').within(() => {
        cy.contains('Selected:').should('be.visible');
        cy.contains('BEGINNER').should('be.visible');
      });
      
      // Click INTERMEDIATE option
      cy.get('[data-testid="technical-levels-filter"]').within(() => {
        cy.contains('INTERMEDIATE').click();
      });
      
      // Verify both are selected
      cy.get('[data-testid="technical-levels-filter"]').within(() => {
        cy.contains('Selected:').should('be.visible');
      });
      
      // URL should reflect the selections
      cy.url().should('include', 'technical_levels=BEGINNER');
      cy.url().should('include', 'technical_levels=INTERMEDIATE');
    });

    it('should allow easy removal of selected options', () => {
      // Select multiple options first
      cy.get('[data-testid="technical-levels-filter"]').within(() => {
        cy.contains('BEGINNER').click();
        cy.contains('INTERMEDIATE').click();
      });
      
      // Remove BEGINNER by clicking the X
      cy.get('[data-testid="technical-levels-filter"]').within(() => {
        cy.contains('BEGINNER').parent().find('svg').click();
      });
      
      // Verify BEGINNER is removed but INTERMEDIATE remains
      cy.url().should('not.include', 'technical_levels=BEGINNER');
      cy.url().should('include', 'technical_levels=INTERMEDIATE');
    });

    it('should have consistent input field styling', () => {
      // Check text inputs have consistent height and styling
      cy.get('[data-testid="key-features-search-filter"]').within(() => {
        cy.get('input').should('have.class', 'h-11');
        cy.get('input').should('have.class', 'bg-white');
      });
      
      cy.get('[data-testid="use-cases-search-filter"]').within(() => {
        cy.get('input').should('have.class', 'h-11');
        cy.get('input').should('have.class', 'bg-white');
      });
    });

    it('should handle boolean filter clicks reliably', () => {
      // Test clicking the entire boolean filter area
      cy.get('[data-testid="has-api-filter"]').click();
      cy.url().should('include', 'has_api=true');
      
      // Click again to uncheck
      cy.get('[data-testid="has-api-filter"]').click();
      cy.url().should('not.include', 'has_api=true');
      
      // Test clicking just the checkbox
      cy.get('[data-testid="has-free-tier-filter"]').within(() => {
        cy.get('input[type="checkbox"]').click();
      });
      cy.url().should('include', 'has_free_tier=true');
    });
  });

  describe('Multiple Entity Types UI', () => {
    it('should display multiple entity filter sections cleanly', () => {
      // Select multiple entity types
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.get('[data-testid="entity-type-filter"]').contains('Course').click();
      cy.wait(1000);
      
      // Both sections should be visible
      cy.get('[data-testid="ai-tool-filters"]').should('be.visible');
      cy.get('[data-testid="course-filters"]').should('be.visible');
      
      // Each should have their own header
      cy.contains('AI Tool Filters').should('be.visible');
      cy.contains('Course Filters').should('be.visible');
    });

    it('should maintain consistent styling across entity types', () => {
      cy.get('[data-testid="entity-type-filter"]').contains('Job').click();
      cy.wait(1000);
      
      // Check Job filters have consistent styling
      cy.get('[data-testid="job-filters"]').should('be.visible');
      cy.get('[data-testid="employment-types-filter"]').should('be.visible');
      
      // Test multiselect in Job filters
      cy.get('[data-testid="employment-types-filter"]').within(() => {
        cy.contains('Full-time').click();
      });
      
      cy.url().should('include', 'employment_types=Full-time');
    });
  });

  describe('Responsive Design', () => {
    it('should work well on mobile viewport', () => {
      cy.viewport(375, 667); // iPhone SE
      
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.wait(1000);
      
      // Filters should still be usable on mobile
      cy.get('[data-testid="technical-levels-filter"]').should('be.visible');
      cy.get('[data-testid="technical-levels-filter"]').within(() => {
        cy.contains('BEGINNER').click();
      });
      
      cy.url().should('include', 'technical_levels=BEGINNER');
    });

    it('should work well on tablet viewport', () => {
      cy.viewport(768, 1024); // iPad
      
      cy.get('[data-testid="entity-type-filter"]').contains('Course').click();
      cy.wait(1000);
      
      // Grid layout should adapt
      cy.get('[data-testid="course-filters"]').should('be.visible');
      cy.get('[data-testid="skill-levels-filter"]').within(() => {
        cy.contains('INTERMEDIATE').click();
      });
      
      cy.url().should('include', 'skill_levels=INTERMEDIATE');
    });
  });

  describe('Accessibility', () => {
    it('should have proper focus management', () => {
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.wait(1000);
      
      // Tab through boolean filters
      cy.get('[data-testid="has-api-filter"]').focus();
      cy.focused().should('have.attr', 'data-testid', 'has-api-filter');
      
      // Enter should activate the filter
      cy.focused().type('{enter}');
      cy.url().should('include', 'has_api=true');
    });

    it('should have proper labels and ARIA attributes', () => {
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.wait(1000);
      
      // Check labels are properly associated
      cy.get('[data-testid="has-api-filter"]').within(() => {
        cy.get('label').should('exist');
        cy.get('input[type="checkbox"]').should('have.attr', 'id');
      });
      
      // Check text inputs have labels
      cy.get('[data-testid="key-features-search-filter"]').within(() => {
        cy.get('label').should('contain', 'Key Features');
        cy.get('input').should('have.attr', 'placeholder');
      });
    });
  });

  describe('Performance', () => {
    it('should handle rapid filter changes without issues', () => {
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.wait(1000);
      
      // Rapidly toggle multiple filters
      cy.get('[data-testid="has-api-filter"]').click();
      cy.get('[data-testid="has-free-tier-filter"]').click();
      cy.get('[data-testid="open-source-filter"]').click();
      cy.get('[data-testid="mobile-support-filter"]').click();
      
      // All should be reflected in URL
      cy.url().should('include', 'has_api=true');
      cy.url().should('include', 'has_free_tier=true');
      cy.url().should('include', 'open_source=true');
      cy.url().should('include', 'mobile_support=true');
    });

    it('should handle multiselect rapid changes', () => {
      cy.get('[data-testid="entity-type-filter"]').contains('AI Tool').click();
      cy.wait(1000);
      
      // Rapidly select multiple technical levels
      cy.get('[data-testid="technical-levels-filter"]').within(() => {
        cy.contains('BEGINNER').click();
        cy.contains('INTERMEDIATE').click();
        cy.contains('ADVANCED').click();
        cy.contains('EXPERT').click();
      });
      
      // All should be in URL
      cy.url().should('include', 'technical_levels=BEGINNER');
      cy.url().should('include', 'technical_levels=INTERMEDIATE');
      cy.url().should('include', 'technical_levels=ADVANCED');
      cy.url().should('include', 'technical_levels=EXPERT');
    });
  });
});
