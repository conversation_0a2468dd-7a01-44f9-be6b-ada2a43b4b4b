{"success": true, "data": [{"id": "1", "name": "ChatGPT", "description": "Advanced AI chatbot for natural language conversations", "entity_type": {"id": "1", "name": "AI Tool", "slug": "ai-tool"}, "categories": [{"id": "1", "name": "Productivity", "slug": "productivity"}], "tags": [{"id": "1", "name": "NLP", "slug": "nlp"}, {"id": "2", "name": "<PERSON><PERSON><PERSON>", "slug": "chatbot"}], "features": [{"id": "1", "name": "API Access", "slug": "api-access"}], "website_url": "https://chat.openai.com", "logo_url": "https://example.com/chatgpt-logo.png", "rating": 4.5, "review_count": 1250, "has_free_tier": true, "api_access": true, "pricing_model": "FREEMIUM", "price_range": "MEDIUM", "created_at": "2023-01-15T10:00:00Z", "updated_at": "2024-01-15T10:00:00Z"}, {"id": "2", "name": "TensorFlow", "description": "Open-source machine learning framework", "entity_type": {"id": "2", "name": "Library", "slug": "library"}, "categories": [{"id": "2", "name": "Development", "slug": "development"}], "tags": [{"id": "3", "name": "Machine Learning", "slug": "machine-learning"}, {"id": "4", "name": "Deep Learning", "slug": "deep-learning"}], "features": [{"id": "2", "name": "Open Source", "slug": "open-source"}], "website_url": "https://tensorflow.org", "logo_url": "https://example.com/tensorflow-logo.png", "rating": 4.8, "review_count": 2100, "has_free_tier": true, "api_access": true, "pricing_model": "FREE", "price_range": "FREE", "created_at": "2022-06-10T14:30:00Z", "updated_at": "2024-01-10T14:30:00Z"}, {"id": "3", "name": "NVIDIA RTX 4090", "description": "High-performance GPU for AI and gaming", "entity_type": {"id": "3", "name": "Hardware", "slug": "hardware"}, "categories": [{"id": "3", "name": "Hardware", "slug": "hardware"}], "tags": [{"id": "5", "name": "GPU", "slug": "gpu"}, {"id": "6", "name": "Gaming", "slug": "gaming"}], "features": [{"id": "3", "name": "High Performance", "slug": "high-performance"}], "website_url": "https://nvidia.com/rtx-4090", "logo_url": "https://example.com/nvidia-logo.png", "rating": 4.7, "review_count": 850, "has_free_tier": false, "api_access": false, "pricing_model": "PAID", "price_range": "HIGH", "created_at": "2023-10-12T09:15:00Z", "updated_at": "2024-01-12T09:15:00Z"}], "meta": {"total": 3, "page": 1, "limit": 20, "totalPages": 1}}