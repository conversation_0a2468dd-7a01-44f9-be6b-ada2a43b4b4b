{"success": true, "data": [{"id": "1", "name": "ChatGPT", "description": "Advanced AI chatbot for natural language conversations", "entity_type": {"id": "1", "name": "AI Tool", "slug": "ai-tool"}, "categories": [{"id": "1", "name": "Productivity", "slug": "productivity"}], "rating": 4.5, "review_count": 1250, "has_free_tier": true}, {"id": "2", "name": "TensorFlow", "description": "Open-source machine learning framework", "entity_type": {"id": "2", "name": "Library", "slug": "library"}, "categories": [{"id": "2", "name": "Development", "slug": "development"}], "rating": 4.8, "review_count": 2100, "has_free_tier": true}, {"id": "3", "name": "PyTorch", "description": "Dynamic neural network framework", "entity_type": {"id": "2", "name": "Library", "slug": "library"}, "categories": [{"id": "2", "name": "Development", "slug": "development"}], "rating": 4.7, "review_count": 1800, "has_free_tier": true}, {"id": "4", "name": "Hugging Face", "description": "Platform for machine learning models", "entity_type": {"id": "4", "name": "Platform", "slug": "platform"}, "categories": [{"id": "1", "name": "Productivity", "slug": "productivity"}], "rating": 4.6, "review_count": 950, "has_free_tier": true}, {"id": "5", "name": "OpenAI API", "description": "API for accessing OpenAI models", "entity_type": {"id": "3", "name": "API", "slug": "api"}, "categories": [{"id": "2", "name": "Development", "slug": "development"}], "rating": 4.4, "review_count": 2200, "has_free_tier": false}, {"id": "6", "name": "Anthropic <PERSON>", "description": "Constitutional AI assistant", "entity_type": {"id": "1", "name": "AI Tool", "slug": "ai-tool"}, "categories": [{"id": "1", "name": "Productivity", "slug": "productivity"}], "rating": 4.3, "review_count": 780, "has_free_tier": true}, {"id": "7", "name": "Stable Diffusion", "description": "Text-to-image generation model", "entity_type": {"id": "5", "name": "Model", "slug": "model"}, "categories": [{"id": "3", "name": "Creative", "slug": "creative"}], "rating": 4.5, "review_count": 1400, "has_free_tier": true}, {"id": "8", "name": "DALL-E 2", "description": "AI image generation tool", "entity_type": {"id": "1", "name": "AI Tool", "slug": "ai-tool"}, "categories": [{"id": "3", "name": "Creative", "slug": "creative"}], "rating": 4.2, "review_count": 1100, "has_free_tier": false}, {"id": "9", "name": "Midjourney", "description": "AI art generation platform", "entity_type": {"id": "4", "name": "Platform", "slug": "platform"}, "categories": [{"id": "3", "name": "Creative", "slug": "creative"}], "rating": 4.6, "review_count": 1600, "has_free_tier": false}, {"id": "10", "name": "GitHub Copilot", "description": "AI-powered code completion", "entity_type": {"id": "1", "name": "AI Tool", "slug": "ai-tool"}, "categories": [{"id": "2", "name": "Development", "slug": "development"}], "rating": 4.1, "review_count": 3200, "has_free_tier": false}], "meta": {"total": 50, "page": 1, "limit": 10, "totalPages": 5}}