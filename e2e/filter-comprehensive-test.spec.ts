import { test, expect, Page } from '@playwright/test';

test.describe('Comprehensive Filter Testing', () => {
  let page: Page;

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage;
    await page.goto('/browse');
    await page.waitForLoadState('networkidle');
  });

  test('Boolean Filters - Has Free Tier', async () => {
    console.log('🧪 Testing Has Free Tier filter...');
    
    // Test in Advanced Filters section
    await page.click('[data-testid="advanced-filters-trigger"]');
    await page.waitForSelector('[data-testid="advanced-filters-content"]');
    
    const hasFreeTierCheckbox = page.locator('#hasFreeTier');
    
    // Initial state should be unchecked
    await expect(hasFreeTierCheckbox).not.toBeChecked();
    
    // Click to check
    await hasFreeTierCheckbox.click();
    await expect(hasFreeTierCheckbox).toBeChecked();
    
    // Click to uncheck
    await hasFreeTierCheckbox.click();
    await expect(hasFreeTierCheckbox).not.toBeChecked();
    
    // Test clicking the label/container
    const hasFreeTierContainer = page.locator('label[for="hasFreeTier"]').first();
    await hasFreeTierContainer.click();
    await expect(hasFreeTierCheckbox).toBeChecked();
    
    console.log('✅ Has Free Tier basic functionality works');
  });

  test('Multi-Select Filters - Pricing Models', async () => {
    console.log('🧪 Testing Pricing Models multi-select...');
    
    await page.click('[data-testid="advanced-filters-trigger"]');
    await page.waitForSelector('[data-testid="advanced-filters-content"]');
    
    // Test pricing model checkboxes
    const freeCheckbox = page.locator('#pricing-FREE');
    const freemiumCheckbox = page.locator('#pricing-FREEMIUM');
    const paidCheckbox = page.locator('#pricing-PAID');
    
    // Initial state - all unchecked
    await expect(freeCheckbox).not.toBeChecked();
    await expect(freemiumCheckbox).not.toBeChecked();
    await expect(paidCheckbox).not.toBeChecked();
    
    // Select FREE
    await freeCheckbox.click();
    await expect(freeCheckbox).toBeChecked();
    await expect(freemiumCheckbox).not.toBeChecked();
    
    // Select FREEMIUM (should allow multiple)
    await freemiumCheckbox.click();
    await expect(freeCheckbox).toBeChecked();
    await expect(freemiumCheckbox).toBeChecked();
    
    // Select PAID (should allow multiple)
    await paidCheckbox.click();
    await expect(freeCheckbox).toBeChecked();
    await expect(freemiumCheckbox).toBeChecked();
    await expect(paidCheckbox).toBeChecked();
    
    // Unselect FREE (others should remain)
    await freeCheckbox.click();
    await expect(freeCheckbox).not.toBeChecked();
    await expect(freemiumCheckbox).toBeChecked();
    await expect(paidCheckbox).toBeChecked();
    
    console.log('✅ Pricing Models multi-select works');
  });

  test('Multi-Select Filters - Price Ranges', async () => {
    console.log('🧪 Testing Price Ranges multi-select...');
    
    await page.click('[data-testid="advanced-filters-trigger"]');
    await page.waitForSelector('[data-testid="advanced-filters-content"]');
    
    const freeRangeCheckbox = page.locator('#price-FREE');
    const lowRangeCheckbox = page.locator('#price-LOW');
    const mediumRangeCheckbox = page.locator('#price-MEDIUM');
    
    // Test multiple selections
    await freeRangeCheckbox.click();
    await expect(freeRangeCheckbox).toBeChecked();
    
    await lowRangeCheckbox.click();
    await expect(freeRangeCheckbox).toBeChecked();
    await expect(lowRangeCheckbox).toBeChecked();
    
    await mediumRangeCheckbox.click();
    await expect(freeRangeCheckbox).toBeChecked();
    await expect(lowRangeCheckbox).toBeChecked();
    await expect(mediumRangeCheckbox).toBeChecked();
    
    console.log('✅ Price Ranges multi-select works');
  });

  test('Entity-Specific Filters - Technical Levels', async () => {
    console.log('🧪 Testing Technical Levels for AI Tool...');
    
    // First select AI Tool entity type
    await page.click('[data-testid="entity-type-tool"]');
    await page.waitForTimeout(1000); // Wait for entity-specific filters to appear
    
    await page.click('[data-testid="advanced-filters-trigger"]');
    await page.waitForSelector('[data-testid="advanced-filters-content"]');
    
    // Look for technical levels in entity-specific section
    const beginnerCheckbox = page.locator('#tool-technical_levels-BEGINNER');
    const intermediateCheckbox = page.locator('#tool-technical_levels-INTERMEDIATE');
    const advancedCheckbox = page.locator('#tool-technical_levels-ADVANCED');
    
    if (await beginnerCheckbox.isVisible()) {
      // Test multiple selections
      await beginnerCheckbox.click();
      await expect(beginnerCheckbox).toBeChecked();
      
      await intermediateCheckbox.click();
      await expect(beginnerCheckbox).toBeChecked();
      await expect(intermediateCheckbox).toBeChecked();
      
      await advancedCheckbox.click();
      await expect(beginnerCheckbox).toBeChecked();
      await expect(intermediateCheckbox).toBeChecked();
      await expect(advancedCheckbox).toBeChecked();
      
      // Test deselection
      await beginnerCheckbox.click();
      await expect(beginnerCheckbox).not.toBeChecked();
      await expect(intermediateCheckbox).toBeChecked();
      await expect(advancedCheckbox).toBeChecked();
      
      console.log('✅ Technical Levels multi-select works');
    } else {
      console.log('⚠️ Technical Levels not visible - may need entity type selection');
    }
  });

  test('Container Click vs Checkbox Click Conflicts', async () => {
    console.log('🧪 Testing click handler conflicts...');
    
    await page.click('[data-testid="advanced-filters-trigger"]');
    await page.waitForSelector('[data-testid="advanced-filters-content"]');
    
    // Test clicking container vs checkbox directly
    const hasFreeTierCheckbox = page.locator('#hasFreeTier');
    const hasFreeTierContainer = page.locator('label[for="hasFreeTier"]').first();
    
    // Click container
    await hasFreeTierContainer.click();
    const isCheckedAfterContainer = await hasFreeTierCheckbox.isChecked();
    
    // Click checkbox directly
    await hasFreeTierCheckbox.click();
    const isCheckedAfterCheckbox = await hasFreeTierCheckbox.isChecked();
    
    // Should toggle properly
    expect(isCheckedAfterContainer).toBe(!isCheckedAfterCheckbox);
    
    console.log('✅ Click handlers work without conflicts');
  });

  test('Filter State Persistence', async () => {
    console.log('🧪 Testing filter state persistence...');
    
    await page.click('[data-testid="advanced-filters-trigger"]');
    await page.waitForSelector('[data-testid="advanced-filters-content"]');
    
    // Set some filters
    await page.click('#hasFreeTier');
    await page.click('#pricing-FREE');
    await page.click('#price-LOW');
    
    // Check URL parameters
    const url = page.url();
    expect(url).toContain('hasFreeTier=true');
    expect(url).toContain('pricingModels=FREE');
    expect(url).toContain('priceRanges=LOW');
    
    // Refresh page
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Check if filters are still selected
    await page.click('[data-testid="advanced-filters-trigger"]');
    await page.waitForSelector('[data-testid="advanced-filters-content"]');
    
    await expect(page.locator('#hasFreeTier')).toBeChecked();
    await expect(page.locator('#pricing-FREE')).toBeChecked();
    await expect(page.locator('#price-LOW')).toBeChecked();
    
    console.log('✅ Filter state persists correctly');
  });

  test('Business Filters Component', async () => {
    console.log('🧪 Testing Business Filters component...');
    
    // This tests the BusinessFilters component specifically
    await page.click('[data-testid="advanced-filters-trigger"]');
    await page.waitForSelector('[data-testid="advanced-filters-content"]');
    
    // Look for business-specific filters
    const employeeFilters = page.locator('[id^="employee-"]');
    const fundingFilters = page.locator('[id^="funding-"]');
    
    if (await employeeFilters.first().isVisible()) {
      await employeeFilters.first().click();
      await expect(employeeFilters.first()).toBeChecked();
      
      // Test multiple employee count selections
      if (await employeeFilters.nth(1).isVisible()) {
        await employeeFilters.nth(1).click();
        await expect(employeeFilters.first()).toBeChecked();
        await expect(employeeFilters.nth(1)).toBeChecked();
      }
    }
    
    console.log('✅ Business Filters component works');
  });
});
