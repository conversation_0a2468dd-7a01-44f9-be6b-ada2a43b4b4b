import { test, expect, Page } from '@playwright/test';

// Test configuration
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'admin123';
const BASE_URL = 'http://localhost:3000';

test.describe('Admin Entity Management', () => {
  let page: Page;

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage;
    
    // Navigate to login page
    await page.goto(`${BASE_URL}/login`);
    
    // Login as admin
    await page.fill('input[type="email"]', ADMIN_EMAIL);
    await page.fill('input[type="password"]', ADMIN_PASSWORD);
    await page.click('button[type="submit"]');
    
    // Wait for login to complete and navigate to admin entities page
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    await page.goto(`${BASE_URL}/admin/entities`);
    await page.waitForLoadState('networkidle');
  });

  test('should display admin entities page with proper layout', async () => {
    // Check page title and header
    await expect(page.locator('h1')).toContainText('Entity Management');
    
    // Check search and filter controls
    await expect(page.locator('input[placeholder*="Search entities"]')).toBeVisible();
    await expect(page.locator('text=Filter by status')).toBeVisible();
    await expect(page.locator('text=Filter by type')).toBeVisible();
    
    // Check entities table
    await expect(page.locator('table')).toBeVisible();
    await expect(page.locator('th:has-text("Entity")')).toBeVisible();
    await expect(page.locator('th:has-text("Description")')).toBeVisible();
    await expect(page.locator('th:has-text("Status")')).toBeVisible();
    await expect(page.locator('th:has-text("Actions")')).toBeVisible();
  });

  test('should filter entities by status', async () => {
    // Test status filtering
    await page.click('text=Filter by status');
    await page.click('text=Pending');
    
    // Wait for filter to apply
    await page.waitForTimeout(1000);
    
    // Check that only pending entities are shown
    const statusBadges = page.locator('[class*="bg-yellow"]');
    const count = await statusBadges.count();
    if (count > 0) {
      for (let i = 0; i < count; i++) {
        await expect(statusBadges.nth(i)).toContainText('PENDING');
      }
    }
  });

  test('should filter entities by type', async () => {
    // Test type filtering
    await page.click('text=Filter by type');
    await page.click('text=AI Tools');
    
    // Wait for filter to apply
    await page.waitForTimeout(1000);
    
    // Check that entities are filtered (this will depend on test data)
    const entityRows = page.locator('tbody tr');
    const count = await entityRows.count();
    
    // If there are entities, verify they are tools
    if (count > 0) {
      const firstEntityType = entityRows.first().locator('td').nth(0).locator('div').nth(1).locator('div').nth(1);
      // This test depends on having tool entities in the database
    }
  });

  test('should search entities by name', async () => {
    // Get the first entity name for searching
    const firstEntityName = await page.locator('tbody tr').first().locator('td').first().locator('div').nth(1).locator('div').first().textContent();
    
    if (firstEntityName) {
      // Search for the entity
      await page.fill('input[placeholder*="Search entities"]', firstEntityName.trim());
      await page.waitForTimeout(1000);
      
      // Check that search results contain the searched entity
      await expect(page.locator('tbody tr')).toContainText(firstEntityName.trim());
    }
  });

  test('should open edit modal when edit button is clicked', async () => {
    // Find the first entity's actions dropdown
    const firstActionsButton = page.locator('tbody tr').first().locator('button').last();
    await firstActionsButton.click();
    
    // Click edit option
    await page.click('text=Edit Entity');
    
    // Check that edit modal opens
    await expect(page.locator('text=Edit Entity:')).toBeVisible();
    await expect(page.locator('input[id="name"]')).toBeVisible();
    await expect(page.locator('input[id="website_url"]')).toBeVisible();
    await expect(page.locator('button:has-text("Save Changes")')).toBeVisible();
    await expect(page.locator('button:has-text("Cancel")')).toBeVisible();
  });

  test('should close edit modal when cancel is clicked', async () => {
    // Open edit modal
    const firstActionsButton = page.locator('tbody tr').first().locator('button').last();
    await firstActionsButton.click();
    await page.click('text=Edit Entity');
    
    // Wait for modal to open
    await expect(page.locator('text=Edit Entity:')).toBeVisible();
    
    // Click cancel
    await page.click('button:has-text("Cancel")');
    
    // Check that modal is closed
    await expect(page.locator('text=Edit Entity:')).not.toBeVisible();
  });

  test('should navigate through edit modal tabs', async () => {
    // Open edit modal
    const firstActionsButton = page.locator('tbody tr').first().locator('button').last();
    await firstActionsButton.click();
    await page.click('text=Edit Entity');
    
    // Wait for modal to open
    await expect(page.locator('text=Edit Entity:')).toBeVisible();
    
    // Test tab navigation
    await page.click('text=Categories & Tags');
    await expect(page.locator('text=Categories')).toBeVisible();
    await expect(page.locator('text=Tags')).toBeVisible();
    
    await page.click('text=Metadata & SEO');
    await expect(page.locator('input[id="meta_title"]')).toBeVisible();
    
    await page.click('text=Advanced');
    await expect(page.locator('text=Status')).toBeVisible();
    await expect(page.locator('text=Referral/Affiliate Link')).toBeVisible();
    
    // Go back to basic info
    await page.click('text=Basic Info');
    await expect(page.locator('input[id="name"]')).toBeVisible();
  });

  test('should open delete confirmation modal', async () => {
    // Find the first entity's actions dropdown
    const firstActionsButton = page.locator('tbody tr').first().locator('button').last();
    await firstActionsButton.click();
    
    // Click delete option
    await page.click('text=Delete Entity');
    
    // Check that delete confirmation modal opens
    await expect(page.locator('text=Delete Entity')).toBeVisible();
    await expect(page.locator('text=This action cannot be undone')).toBeVisible();
    await expect(page.locator('text=Type')).toBeVisible();
    await expect(page.locator('button:has-text("Delete Entity")')).toBeVisible();
    await expect(page.locator('button:has-text("Cancel")')).toBeVisible();
  });

  test('should close delete modal when cancel is clicked', async () => {
    // Open delete modal
    const firstActionsButton = page.locator('tbody tr').first().locator('button').last();
    await firstActionsButton.click();
    await page.click('text=Delete Entity');
    
    // Wait for modal to open
    await expect(page.locator('text=Delete Entity')).toBeVisible();
    
    // Click cancel
    await page.click('button:has-text("Cancel")');
    
    // Check that modal is closed
    await expect(page.locator('text=Delete Entity')).not.toBeVisible();
  });

  test('should change entity status through dropdown', async () => {
    // Find a pending entity if available
    const pendingEntity = page.locator('tbody tr').filter({ hasText: 'PENDING' }).first();
    
    if (await pendingEntity.count() > 0) {
      // Click actions dropdown for pending entity
      await pendingEntity.locator('button').last().click();
      
      // Click approve
      await page.click('text=Approve');
      
      // Wait for status update
      await page.waitForTimeout(2000);
      
      // Check that status changed (this might require page refresh or real-time updates)
      // Note: This test might need adjustment based on how status updates are reflected in the UI
    }
  });

  test('should handle pagination if multiple pages exist', async () => {
    // Check if pagination controls exist
    const nextButton = page.locator('button:has-text("Next")');
    const prevButton = page.locator('button:has-text("Previous")');
    
    if (await nextButton.count() > 0) {
      // Test pagination
      const isNextEnabled = await nextButton.isEnabled();
      if (isNextEnabled) {
        await nextButton.click();
        await page.waitForTimeout(1000);
        
        // Check that we're on a different page
        await expect(prevButton).toBeEnabled();
      }
    }
  });

  test('should clear filters when clear button is clicked', async () => {
    // Apply some filters first
    await page.click('text=Filter by status');
    await page.click('text=Pending');
    await page.waitForTimeout(1000);
    
    // Clear filters
    await page.click('button:has-text("Clear Filters")');
    await page.waitForTimeout(1000);
    
    // Check that filters are reset
    await expect(page.locator('text=Filter by status')).toBeVisible();
    await expect(page.locator('text=Filter by type')).toBeVisible();
  });

  test('should open entity details in new tab', async () => {
    // Find the first entity's actions dropdown
    const firstActionsButton = page.locator('tbody tr').first().locator('button').last();
    await firstActionsButton.click();
    
    // Check that "View Details" option exists
    await expect(page.locator('text=View Details')).toBeVisible();
    
    // Note: Testing actual new tab opening requires more complex setup
    // For now, we just verify the option exists
  });
});
