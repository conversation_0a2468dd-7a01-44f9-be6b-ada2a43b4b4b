-- Script to make a user an admin
-- Run this in Supabase SQL Editor to grant admin access

-- Update user role to admin
UPDATE public.users 
SET role = 'admin'::user_role 
WHERE email = '<EMAIL>';

-- Verify the change
SELECT id, email, role, status, display_name, created_at
FROM public.users 
WHERE email = '<EMAIL>';

-- Optional: Create additional admin users if needed
-- UPDATE public.users 
-- SET role = 'admin'::user_role 
-- WHERE email = '<EMAIL>';

-- Show all admin users
SELECT id, email, role, status, display_name, created_at
FROM public.users 
WHERE role = 'admin'::user_role
ORDER BY created_at;
