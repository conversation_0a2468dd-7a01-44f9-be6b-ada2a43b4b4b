/**
 * Comprehensive API contract test runner for enhanced entity forms
 * Run with: node scripts/test-api-contract.js
 */

const API_BASE_URL = 'https://ai-nav.onrender.com';

// Test payloads to understand the API contract for all enhanced entity types
const fieldTests = [
  // Enhanced Tool Details Tests
  {
    name: 'Enhanced Tool - All New Fields',
    payload: {
      name: 'Enhanced Test Tool',
      description: 'Test description for enhanced tool',
      website_url: 'https://example.com',
      entity_type_id: 'test-id',
      tool_details: {
        technical_level: 'INTERMEDIATE',
        learning_curve: 'MEDIUM',
        pricing_model: 'FREEMIUM',
        price_range: 'LOW',
        has_free_tier: true,
        has_api: true,
        api_access: true,
        has_live_chat: false,
        open_source: false,
        mobile_support: true,
        trial_available: true,
        demo_available: true,
        community_url: 'https://community.example.com',
        customization_level: 'High',
        current_version: 'v2.1.0',
        pricing_url: 'https://example.com/pricing',
        support_email: '<EMAIL>',
        pricing_details: 'Free tier with 1000 requests/month',
        key_features: ['AI-powered analysis', 'Real-time collaboration'],
        use_cases: ['Content creation', 'Data analysis'],
        target_audience: ['Developers', 'Data scientists'],
        support_channels: ['Email', 'Live chat'],
        frameworks: ['React', 'Vue'],
        integrations: ['Slack', 'Discord'],
        libraries: ['TensorFlow', 'PyTorch'],
        platforms: ['Web', 'Desktop'],
        programming_languages: ['Python', 'JavaScript'],
        supported_os: ['Windows', 'macOS', 'Linux'],
        deployment_options: ['Cloud', 'On-premise']
      }
    }
  },
  {
    name: 'Empty tool_details',
    payload: {
      name: 'Test Tool 1',
      description: 'Test description',
      website_url: 'https://example.com',
      entity_type_id: 'test-id',
      tool_details: {}
    }
  },

  // Enhanced Course Details Tests
  {
    name: 'Enhanced Course - All Fields',
    payload: {
      name: 'Enhanced Test Course',
      description: 'Test description for enhanced course',
      website_url: 'https://example.com/course',
      entity_type_id: 'test-id',
      course_details: {
        instructor_name: 'Dr. Jane Smith',
        duration_text: '8 weeks',
        skill_level: 'INTERMEDIATE',
        language: 'English',
        certificate_available: true,
        syllabus_url: 'https://example.com/syllabus',
        enrollment_count: 1500,
        prerequisites: ['Basic programming', 'HTML/CSS'],
        learning_outcomes: ['Build web apps', 'Understand React']
      }
    }
  },

  // Enhanced Agency Details Tests
  {
    name: 'Enhanced Agency - All Fields',
    payload: {
      name: 'Enhanced Test Agency',
      description: 'Test description for enhanced agency',
      website_url: 'https://example.com/agency',
      entity_type_id: 'test-id',
      agency_details: {
        portfolio_url: 'https://agency.com/portfolio',
        team_size: '10-50 employees',
        contact_email: '<EMAIL>',
        region_served: 'North America',
        location_summary: 'San Francisco, CA | Remote-first',
        pricing_info: 'Hourly rates from $150-300',
        services_offered: ['Web development', 'Mobile apps'],
        specializations: ['E-commerce', 'SaaS'],
        industry_focus: ['Healthcare', 'Finance'],
        target_client_size: ['SMB', 'Enterprise']
      }
    }
  },

  // Enhanced Newsletter Details Tests
  {
    name: 'Enhanced Newsletter - All Fields',
    payload: {
      name: 'Enhanced Test Newsletter',
      description: 'Test description for enhanced newsletter',
      website_url: 'https://example.com/newsletter',
      entity_type_id: 'test-id',
      newsletter_details: {
        author_name: 'John Doe',
        frequency: 'Weekly',
        archive_url: 'https://newsletter.com/archive',
        subscribe_url: 'https://newsletter.com/subscribe',
        subscriber_count: 10000,
        target_audience: 'AI enthusiasts',
        main_topics: ['AI news', 'Tech trends']
      }
    }
  },

  // Software Details Tests
  {
    name: 'Software - All Fields',
    payload: {
      name: 'Test Software Platform',
      description: 'Test description for software',
      website_url: 'https://example.com/software',
      entity_type_id: 'test-id',
      software_details: {
        current_version: 'v3.2.1',
        license_type: 'MIT',
        community_url: 'https://community.example.com',
        has_free_tier: true,
        has_live_chat: true,
        price_range: 'MEDIUM',
        pricing_model: 'SUBSCRIPTION',
        pricing_url: 'https://example.com/pricing',
        support_email: '<EMAIL>',
        api_access: true,
        customization_level: 'High',
        demo_available: true,
        has_api: true,
        mobile_support: true,
        open_source: false,
        trial_available: true,
        pricing_details: 'Starting at $99/month',
        key_features: ['Advanced analytics', 'Real-time sync'],
        frameworks: ['React', 'Vue', 'Angular'],
        libraries: ['D3.js', 'Chart.js'],
        programming_languages: ['JavaScript', 'Python'],
        integrations: ['Slack', 'GitHub'],
        platform_compatibility: ['Web', 'Desktop'],
        supported_os: ['Windows', 'macOS'],
        deployment_options: ['Cloud', 'On-premise'],
        support_channels: ['Email', 'Phone'],
        target_audience: ['Developers', 'Analysts'],
        use_cases: ['Data visualization', 'Reporting']
      }
    }
  },

  // Model Details Tests
  {
    name: 'Model - All Fields',
    payload: {
      name: 'Test AI Model',
      description: 'Test description for AI model',
      website_url: 'https://example.com/model',
      entity_type_id: 'test-id',
      model_details: {
        model_architecture: 'Transformer',
        training_dataset: 'Common Crawl',
        license: 'Apache 2.0',
        performance_metrics: {
          accuracy: 0.95,
          f1_score: 0.92,
          perplexity: 15.2
        },
        input_data_types: ['Text', 'Image'],
        output_data_types: ['Text', 'Embeddings'],
        frameworks: ['PyTorch', 'Hugging Face'],
        libraries: ['Transformers', 'Tokenizers'],
        deployment_options: ['Cloud API', 'Local inference'],
        use_cases: ['Text generation', 'Classification'],
        target_audience: ['Researchers', 'Developers']
      }
    }
  },

  // Dataset Details Tests
  {
    name: 'Dataset - All Fields',
    payload: {
      name: 'Test Dataset',
      description: 'Test description for dataset',
      website_url: 'https://example.com/dataset',
      entity_type_id: 'test-id',
      dataset_details: {
        license: 'CC BY 4.0',
        size_in_bytes: **********,
        format: 'JSON',
        source_url: 'https://example.com/download',
        collection_method: 'Web scraping',
        update_frequency: 'Monthly',
        access_notes: 'Free download with registration'
      }
    }
  },

  // Platform Details Tests
  {
    name: 'Platform - All Fields',
    payload: {
      name: 'Test Platform',
      description: 'Test description for platform',
      website_url: 'https://example.com/platform',
      entity_type_id: 'test-id',
      platform_details: {
        platform_type: 'PaaS',
        documentation_url: 'https://docs.example.com',
        community_url: 'https://community.example.com',
        pricing_model: 'FREEMIUM',
        price_range: 'MEDIUM',
        has_free_tier: true,
        has_api: true,
        has_live_chat: true,
        api_access: true,
        demo_available: true,
        trial_available: true,
        mobile_support: false,
        customization_level: 'Medium',
        pricing_url: 'https://example.com/pricing',
        support_email: '<EMAIL>',
        pricing_details: 'Free tier + paid plans',
        deployment_options: ['Cloud', 'Multi-cloud'],
        target_audience: ['DevOps', 'Developers'],
        supported_os: ['Web-based'],
        integrations: ['AWS', 'Azure', 'GCP'],
        key_services: ['Hosting', 'Analytics'],
        use_cases: ['Web hosting', 'App deployment']
      }
    }
  }
];

async function testApiContract() {
  console.log('🧪 Testing Enhanced Entity Forms API Contract\n');
  console.log('📋 Testing all enhanced entity types with comprehensive field sets\n');

  // Get auth token from environment or prompt user
  const authToken = process.env.TEST_AUTH_TOKEN;

  if (!authToken) {
    console.log('❌ Please set TEST_AUTH_TOKEN environment variable with a valid auth token');
    console.log('   You can get this from your browser\'s developer tools when logged in');
    console.log('   Example: TEST_AUTH_TOKEN=your_token_here node scripts/test-api-contract.js');
    return;
  }

  const results = {
    successful: [],
    failed: [],
    fieldErrors: {}
  };

  for (const test of fieldTests) {
    console.log(`🔍 Testing: ${test.name}`);
    console.log(`📤 Entity Type: ${Object.keys(test.payload).find(key => key.endsWith('_details'))}`);

    try {
      const response = await fetch(`${API_BASE_URL}/entities`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
        body: JSON.stringify(test.payload),
      });

      const responseText = await response.text();

      if (response.ok) {
        console.log(`✅ Success (${response.status})`);
        results.successful.push(test.name);
      } else {
        console.log(`❌ Error (${response.status}):`, responseText);
        results.failed.push({
          name: test.name,
          status: response.status,
          error: responseText
        });

        // Parse field-specific errors
        if (responseText.includes('should not exist')) {
          const fieldMatch = responseText.match(/property (\w+) should not exist/g);
          if (fieldMatch) {
            const entityType = Object.keys(test.payload).find(key => key.endsWith('_details'));
            if (!results.fieldErrors[entityType]) {
              results.fieldErrors[entityType] = [];
            }
            fieldMatch.forEach(match => {
              const field = match.match(/property (\w+) should not exist/)[1];
              if (!results.fieldErrors[entityType].includes(field)) {
                results.fieldErrors[entityType].push(field);
              }
            });
          }
        }
      }
    } catch (error) {
      console.log(`❌ Network Error:`, error.message);
      results.failed.push({
        name: test.name,
        status: 'NETWORK_ERROR',
        error: error.message
      });
    }

    console.log('---\n');
  }

  // Summary Report
  console.log('📊 COMPREHENSIVE TEST RESULTS SUMMARY');
  console.log('=====================================\n');

  console.log(`✅ Successful Tests: ${results.successful.length}`);
  results.successful.forEach(name => console.log(`   - ${name}`));

  console.log(`\n❌ Failed Tests: ${results.failed.length}`);
  results.failed.forEach(test => console.log(`   - ${test.name} (${test.status})`));

  if (Object.keys(results.fieldErrors).length > 0) {
    console.log('\n🚫 FIELDS NOT ACCEPTED BY BACKEND:');
    Object.entries(results.fieldErrors).forEach(([entityType, fields]) => {
      console.log(`\n   ${entityType}:`);
      fields.forEach(field => console.log(`     - ${field}`));
    });
  }

  console.log('\n💡 RECOMMENDATIONS:');
  console.log('   1. Review failed tests to identify backend schema gaps');
  console.log('   2. Update backend to accept missing fields for comprehensive data capture');
  console.log('   3. Consider which fields are essential vs. nice-to-have');
  console.log('   4. Update frontend forms to only include accepted fields initially');
  console.log('\n🎯 GOAL: Make AI Navigator the most comprehensive AI platform with maximum data richness!');
}

// Run the test
testApiContract().catch(console.error);
