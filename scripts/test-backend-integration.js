/**
 * Test backend integration without requiring auth token
 * Tests entity types endpoint and validates our form mapping
 */

const API_BASE_URL = 'https://ai-nav.onrender.com';

async function testBackendIntegration() {
  console.log('🧪 Testing Backend Integration');
  console.log('==============================\n');

  try {
    // Test 1: Entity Types Endpoint
    console.log('📡 Testing Entity Types Endpoint...');
    const response = await fetch(`${API_BASE_URL}/entity-types`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const entityTypes = await response.json();
    console.log('✅ Entity types endpoint working');
    console.log(`📊 Found ${entityTypes.length} entity types\n`);

    // Test 2: Validate Entity Types Structure
    console.log('🔍 Validating Entity Types Structure:');
    entityTypes.forEach((type, index) => {
      const hasRequiredFields = type.id && type.name && type.slug;
      const status = hasRequiredFields ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${type.name} (${type.slug})`);
      
      if (!hasRequiredFields) {
        console.log(`   ❌ Missing required fields: ${!type.id ? 'id ' : ''}${!type.name ? 'name ' : ''}${!type.slug ? 'slug' : ''}`);
      }
    });

    // Test 3: Check Form Mapping Coverage
    console.log('\n🗺️  Checking Form Mapping Coverage:');
    const formMapping = {
      'ai-tool': 'ToolDetailsForm',
      'api': 'SoftwareDetailsForm',
      'dataset': 'DatasetDetailsForm',
      'library': 'SoftwareDetailsForm',
      'model': 'ModelDetailsForm',
      'platform': 'PlatformDetailsForm',
      'service': 'PlatformDetailsForm',
      'test-rel-type-prisma': 'DefaultDetailsForm'
    };

    const backendSlugs = entityTypes.map(type => type.slug);
    const mappedSlugs = Object.keys(formMapping);
    
    const unmappedTypes = backendSlugs.filter(slug => !formMapping[slug]);
    const extraMappings = mappedSlugs.filter(slug => !backendSlugs.includes(slug));

    console.log(`📊 Backend Types: ${backendSlugs.length}`);
    console.log(`📊 Mapped Types: ${mappedSlugs.length}`);
    console.log(`📊 Unmapped Types: ${unmappedTypes.length}`);
    console.log(`📊 Extra Mappings: ${extraMappings.length}`);

    if (unmappedTypes.length > 0) {
      console.log('\n❌ Unmapped Backend Types:');
      unmappedTypes.forEach(slug => console.log(`   - ${slug}`));
    }

    if (extraMappings.length > 0) {
      console.log('\n⚠️  Extra Form Mappings (future types):');
      extraMappings.forEach(slug => console.log(`   - ${slug}`));
    }

    // Test 4: Form Distribution Analysis
    console.log('\n📈 Form Distribution Analysis:');
    const formCounts = {};
    backendSlugs.forEach(slug => {
      const form = formMapping[slug] || 'UNMAPPED';
      formCounts[form] = (formCounts[form] || 0) + 1;
    });

    Object.entries(formCounts).forEach(([form, count]) => {
      const types = backendSlugs.filter(slug => formMapping[slug] === form);
      console.log(`- ${form}: ${count} types (${types.join(', ')})`);
    });

    // Test 5: Success Summary
    const allMapped = unmappedTypes.length === 0;
    console.log('\n🎯 Integration Test Results:');
    console.log(`${allMapped ? '✅' : '❌'} Form Mapping: ${allMapped ? 'Complete' : 'Incomplete'}`);
    console.log('✅ Entity Types API: Working');
    console.log('✅ Data Structure: Valid');
    
    if (allMapped) {
      console.log('\n🚀 SUCCESS: All backend entity types are properly mapped to enhanced forms!');
      console.log('🎉 Ready for full E2E testing and production deployment!');
    } else {
      console.log('\n⚠️  WARNING: Some backend types are not mapped to forms.');
      console.log('💡 Consider adding form mappings for unmapped types.');
    }

  } catch (error) {
    console.error('❌ Backend Integration Test Failed:');
    console.error(`   Error: ${error.message}`);
    console.error('\n💡 Troubleshooting:');
    console.error('   - Check internet connection');
    console.error('   - Verify backend API is running');
    console.error('   - Check API endpoint URL');
  }
}

// Run the test
testBackendIntegration();
