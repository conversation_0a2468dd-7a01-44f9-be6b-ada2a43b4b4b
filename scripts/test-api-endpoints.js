/**
 * Comprehensive API endpoint testing without authentication
 */

const API_BASE_URL = 'https://ai-nav.onrender.com';

async function testAPIEndpoints() {
  console.log('🧪 Comprehensive API Endpoint Testing');
  console.log('=====================================\n');

  const results = {
    passed: 0,
    failed: 0,
    total: 0
  };

  async function testEndpoint(name, url, expectedFields = []) {
    results.total++;
    try {
      console.log(`📡 Testing ${name}...`);
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Check if it's an array or has data property
      const items = Array.isArray(data) ? data : (data.data || []);
      
      if (items.length === 0) {
        console.log(`⚠️  ${name}: No data found`);
        results.passed++;
        return;
      }

      // Check first item structure
      const firstItem = items[0];
      const missingFields = expectedFields.filter(field => !(field in firstItem));
      
      if (missingFields.length === 0) {
        console.log(`✅ ${name}: Working (${items.length} items)`);
        results.passed++;
      } else {
        console.log(`❌ ${name}: Missing fields - ${missingFields.join(', ')}`);
        results.failed++;
      }

      // Show sample structure
      if (items.length > 0) {
        const sampleKeys = Object.keys(firstItem).slice(0, 5);
        console.log(`   📋 Sample fields: ${sampleKeys.join(', ')}${sampleKeys.length < Object.keys(firstItem).length ? '...' : ''}`);
      }
      
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
      results.failed++;
    }
    console.log('');
  }

  // Test all public endpoints
  await testEndpoint(
    'Entity Types', 
    `${API_BASE_URL}/entity-types`,
    ['id', 'name', 'slug', 'description']
  );

  await testEndpoint(
    'Categories',
    `${API_BASE_URL}/categories`,
    ['id', 'name', 'description']
  );

  await testEndpoint(
    'Tags',
    `${API_BASE_URL}/tags`,
    ['id', 'name']
  );

  await testEndpoint(
    'Features',
    `${API_BASE_URL}/features`,
    ['id', 'name', 'description']
  );

  await testEndpoint(
    'Entities (Public)',
    `${API_BASE_URL}/entities?limit=5`,
    ['id', 'name', 'website_url', 'description']
  );

  // Test API documentation endpoint
  await testEndpoint(
    'API Documentation',
    `${API_BASE_URL}/api-docs-json`,
    []
  );

  // Summary
  console.log('📊 API Testing Summary:');
  console.log('======================');
  console.log(`✅ Passed: ${results.passed}/${results.total}`);
  console.log(`❌ Failed: ${results.failed}/${results.total}`);
  console.log(`📈 Success Rate: ${Math.round((results.passed / results.total) * 100)}%`);

  if (results.failed === 0) {
    console.log('\n🚀 SUCCESS: All API endpoints are working correctly!');
    console.log('🎉 Backend integration is fully functional!');
  } else {
    console.log('\n⚠️  Some endpoints have issues. Check the details above.');
  }

  return results;
}

// Run the test
testAPIEndpoints();
