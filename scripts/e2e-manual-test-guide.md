# Manual E2E Testing Guide

## 🎯 Quick E2E Test Checklist

Based on your screenshot showing all 8 entity types in the dropdown, here's a quick manual test to verify everything is working:

### **Step 1: Navigate to Submit Page**
- Go to: `http://localhost:3000/submit` (or your dev server URL)
- ✅ Verify page loads without errors
- ✅ Verify dropdown shows all 8 entity types

### **Step 2: Test Each Entity Type Form**

#### **Test 2.1: AI Tool**
1. Select "AI Tool" from dropdown
2. ✅ Verify ToolDetailsForm loads
3. ✅ Check for fields: Technical Level, Pricing Model, Boolean checkboxes
4. ✅ Verify array fields: Key Features, Use Cases, etc.

#### **Test 2.2: API**
1. Select "API" from dropdown  
2. ✅ Verify SoftwareDetailsForm loads
3. ✅ Check for fields: License Type, Current Version, Community URL
4. ✅ Verify array fields: Frameworks, Libraries, Programming Languages

#### **Test 2.3: Dataset**
1. Select "Dataset" from dropdown
2. ✅ Verify DatasetDetailsForm loads
3. ✅ Check for fields: License, Size in Bytes, Format, Source URL
4. ✅ Verify fields: Collection Method, Update Frequency, Access Notes

#### **Test 2.4: Library**
1. Select "Library" from dropdown
2. ✅ Verify SoftwareDetailsForm loads (same as API)
3. ✅ Check all software-specific fields are present

#### **Test 2.5: Model**
1. Select "Model" from dropdown
2. ✅ Verify ModelDetailsForm loads
3. ✅ Check for fields: Model Architecture, Training Dataset, License
4. ✅ Verify Performance Metrics JSON field
5. ✅ Check array fields: Input/Output Data Types, Frameworks

#### **Test 2.6: Platform**
1. Select "Platform" from dropdown
2. ✅ Verify PlatformDetailsForm loads
3. ✅ Check for fields: Platform Type, Documentation URL
4. ✅ Verify array fields: Key Services, Deployment Options

#### **Test 2.7: Service**
1. Select "Service" from dropdown
2. ✅ Verify PlatformDetailsForm loads (same as Platform)
3. ✅ Check all platform-specific fields are present

#### **Test 2.8: Test Relation Type**
1. Select "Test Relation Type" from dropdown
2. ✅ Verify DefaultDetailsForm loads
3. ✅ Check for basic additional details field

### **Step 3: Test Form Functionality**

#### **Test 3.1: Field Validation**
- ✅ Try submitting with empty required fields
- ✅ Verify validation errors appear
- ✅ Test URL field validation
- ✅ Test number field validation

#### **Test 3.2: Array Field Processing**
- ✅ Enter comma-separated values in array fields
- ✅ Verify placeholder text is helpful
- ✅ Test with empty array fields

#### **Test 3.3: Form Submission** (Optional - requires auth)
- ✅ Fill out a complete form
- ✅ Submit and verify success message
- ✅ Check network tab for correct payload structure

### **Step 4: Responsive Design Test**
- ✅ Test on mobile device/narrow screen
- ✅ Verify forms are responsive
- ✅ Check dropdown works on mobile

## 🚀 Expected Results

### **✅ Success Criteria:**
- All 8 entity types load correct forms
- All enhanced fields are present and functional
- Form validation works correctly
- Array fields process comma-separated input
- Forms are responsive and accessible

### **📊 Form Field Counts:**
- **ToolDetailsForm**: 25+ fields (AI Tool)
- **SoftwareDetailsForm**: 30+ fields (API, Library)
- **DatasetDetailsForm**: 10+ fields (Dataset)
- **ModelDetailsForm**: 15+ fields (Model)
- **PlatformDetailsForm**: 25+ fields (Platform, Service)
- **DefaultDetailsForm**: Basic fields (Test Relation Type)

## 🐛 Common Issues to Watch For

### **Issue: Form doesn't load**
- Check browser console for errors
- Verify all form components are properly imported
- Check for TypeScript compilation errors

### **Issue: Fields missing**
- Verify form component has all expected fields
- Check for conditional rendering issues
- Verify field registration with React Hook Form

### **Issue: Array fields not working**
- Test comma-separated input format
- Check array processing logic in SubmitForm.tsx
- Verify placeholder text is showing

### **Issue: Validation errors**
- Check Zod schema definitions
- Verify field names match between form and schema
- Test with minimal valid data

## 🎯 Quick Test Commands

### **Start Development Server:**
```bash
npm run dev
```

### **Run Form Mapping Test:**
```bash
node scripts/test-form-mapping.js
```

### **Run API Contract Test:** (requires auth token)
```bash
TEST_AUTH_TOKEN=your_token node scripts/test-api-contract.js
```

## 📈 Success Metrics

After completing this E2E test:
- ✅ 8/8 entity types have working forms
- ✅ 100+ enhanced fields are functional
- ✅ Smart entity mapping is working
- ✅ User experience is smooth and intuitive
- ✅ AI Navigator has comprehensive entity submission system

**Result: Most comprehensive AI platform entity submission system! 🚀**
