/**
 * Test script to verify form mapping matches the actual backend entity types
 */

// Actual backend entity types (from your screenshot)
const actualBackendTypes = [
  { name: "AI Tool", slug: "ai-tool" },
  { name: "API", slug: "api" },
  { name: "Dataset", slug: "dataset" },
  { name: "Library", slug: "library" },
  { name: "Model", slug: "model" },
  { name: "Platform", slug: "platform" },
  { name: "Service", slug: "service" },
  { name: "Test Relation Type", slug: "test-rel-type-prisma" }
];

// Our form mapping (from SubmitForm.tsx)
const formMapping = {
  'ai-tool': 'ToolDetailsForm',
  'api': 'SoftwareDetailsForm',
  'dataset': 'DatasetDetailsForm',
  'library': 'SoftwareDetailsForm',
  'model': 'ModelDetailsForm',
  'platform': 'PlatformDetailsForm',
  'service': 'PlatformDetailsForm',
  'test-rel-type-prisma': 'DefaultDetailsForm'
};

// Our payload mapping
const payloadMapping = {
  'ai-tool': 'tool_details',
  'api': 'software_details',
  'dataset': 'dataset_details',
  'library': 'software_details',
  'model': 'model_details',
  'platform': 'platform_details',
  'service': 'platform_details',
  'test-rel-type-prisma': 'details'
};

console.log('🧪 Form Mapping Verification Test');
console.log('==================================\n');

console.log('✅ Testing Each Backend Entity Type:\n');

actualBackendTypes.forEach((type, index) => {
  const formUsed = formMapping[type.slug] || '❌ NO MAPPING';
  const payloadKey = payloadMapping[type.slug] || '❌ NO MAPPING';
  const status = formMapping[type.slug] ? '✅' : '❌';
  
  console.log(`${index + 1}. ${status} ${type.name} (${type.slug})`);
  console.log(`   📝 Form: ${formUsed}`);
  console.log(`   📤 Payload: ${payloadKey}`);
  console.log('');
});

// Check for missing mappings
const missingMappings = actualBackendTypes.filter(type => !formMapping[type.slug]);
const extraMappings = Object.keys(formMapping).filter(slug => 
  !actualBackendTypes.some(type => type.slug === slug)
);

console.log('📊 Summary:');
console.log(`- Backend Entity Types: ${actualBackendTypes.length}`);
console.log(`- Mapped Forms: ${Object.keys(formMapping).length}`);
console.log(`- Missing Mappings: ${missingMappings.length}`);
console.log(`- Extra Mappings: ${extraMappings.length}`);

if (missingMappings.length > 0) {
  console.log('\n❌ Missing Mappings:');
  missingMappings.forEach(type => console.log(`   - ${type.name} (${type.slug})`));
}

if (extraMappings.length > 0) {
  console.log('\n⚠️  Extra Mappings (future entity types):');
  extraMappings.forEach(slug => console.log(`   - ${slug}`));
}

console.log('\n🎯 Form Distribution:');
const formCounts = {};
Object.values(formMapping).forEach(form => {
  formCounts[form] = (formCounts[form] || 0) + 1;
});

Object.entries(formCounts).forEach(([form, count]) => {
  const entityTypes = Object.entries(formMapping)
    .filter(([slug, formName]) => formName === form)
    .map(([slug]) => slug);
  console.log(`- ${form}: ${count} types (${entityTypes.join(', ')})`);
});

const allMapped = missingMappings.length === 0;
console.log(`\n${allMapped ? '✅' : '❌'} ${allMapped ? 'All backend entity types are properly mapped!' : 'Some entity types are missing mappings!'}`);
console.log('🚀 Ready for E2E testing!');
