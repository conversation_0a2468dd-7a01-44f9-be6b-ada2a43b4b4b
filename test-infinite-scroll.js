// Comprehensive Infinite Scroll Testing Script
// Run this in the browser console on https://ai-nav-frontend.vercel.app/browse

console.log('🚀 Starting Infinite Scroll Testing...');

// Test configuration
const TESTS = {
  basicScroll: true,
  manualButton: true,
  withFilters: true,
  withSearch: true,
  withSorting: true,
  errorHandling: true
};

// Helper functions
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const scrollToBottom = () => {
  window.scrollTo(0, document.body.scrollHeight);
};

const getEntityCount = () => {
  const cards = document.querySelectorAll('[class*="ResourceCard"], [class*="resource-card"], .group.relative.bg-white');
  return cards.length;
};

const getLoadMoreButton = () => {
  return document.querySelector('button:contains("Load More Resources"), button[class*="load-more"]') || 
         Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('Load More'));
};

const getPaginationInfo = () => {
  const totalText = document.querySelector('[class*="text-gray-600"], [class*="text-gray-500"]');
  if (totalText && totalText.textContent.includes('resources found')) {
    const match = totalText.textContent.match(/(\d+) resources found/);
    return match ? parseInt(match[1]) : null;
  }
  return null;
};

const isLoadingMore = () => {
  return document.querySelector('[class*="Loading more resources"], [class*="animate-spin"]') !== null;
};

// Test functions
async function testBasicInfiniteScroll() {
  console.log('📋 Test 1: Basic Infinite Scroll');
  
  const initialCount = getEntityCount();
  console.log(`Initial entity count: ${initialCount}`);
  
  if (initialCount === 0) {
    console.error('❌ No entities loaded initially');
    return false;
  }
  
  // Scroll to bottom
  scrollToBottom();
  console.log('Scrolled to bottom, waiting for load...');
  
  // Wait for loading to start
  await wait(1000);
  
  let attempts = 0;
  const maxAttempts = 10;
  
  while (attempts < maxAttempts) {
    const currentCount = getEntityCount();
    const loading = isLoadingMore();
    
    console.log(`Attempt ${attempts + 1}: Count=${currentCount}, Loading=${loading}`);
    
    if (currentCount > initialCount) {
      console.log('✅ Infinite scroll working - more entities loaded');
      return true;
    }
    
    if (!loading) {
      // Try scrolling again
      scrollToBottom();
    }
    
    await wait(2000);
    attempts++;
  }
  
  console.error('❌ Infinite scroll failed - no additional entities loaded');
  return false;
}

async function testManualLoadMore() {
  console.log('📋 Test 2: Manual Load More Button');
  
  const initialCount = getEntityCount();
  const loadMoreBtn = getLoadMoreButton();
  
  if (!loadMoreBtn) {
    console.error('❌ Load More button not found');
    return false;
  }
  
  console.log('Found Load More button, clicking...');
  loadMoreBtn.click();
  
  // Wait for loading
  await wait(3000);
  
  const newCount = getEntityCount();
  
  if (newCount > initialCount) {
    console.log('✅ Manual Load More working');
    return true;
  } else {
    console.error('❌ Manual Load More failed');
    return false;
  }
}

async function testWithFilters() {
  console.log('📋 Test 3: Infinite Scroll with Filters');
  
  // Apply a filter (try to find entity type filter)
  const filterCheckbox = document.querySelector('input[type="checkbox"]');
  if (filterCheckbox && !filterCheckbox.checked) {
    filterCheckbox.click();
    console.log('Applied filter, waiting for results...');
    await wait(3000);
  }
  
  return await testBasicInfiniteScroll();
}

async function testWithSearch() {
  console.log('📋 Test 4: Infinite Scroll with Search');
  
  // Find search input
  const searchInput = document.querySelector('input[type="text"], input[placeholder*="search"], input[placeholder*="Search"]');
  if (searchInput) {
    searchInput.value = 'AI';
    searchInput.dispatchEvent(new Event('input', { bubbles: true }));
    console.log('Applied search term, waiting for results...');
    await wait(3000);
  }
  
  return await testBasicInfiniteScroll();
}

async function testWithSorting() {
  console.log('📋 Test 5: Infinite Scroll with Sorting');
  
  // Try to find sort dropdown
  const sortSelect = document.querySelector('select, [role="combobox"]');
  if (sortSelect) {
    // Try to change sort option
    if (sortSelect.tagName === 'SELECT') {
      sortSelect.selectedIndex = 1;
      sortSelect.dispatchEvent(new Event('change', { bubbles: true }));
    }
    console.log('Changed sorting, waiting for results...');
    await wait(3000);
  }
  
  return await testBasicInfiniteScroll();
}

async function testErrorHandling() {
  console.log('📋 Test 6: Error Handling');
  
  // This test checks if the UI handles errors gracefully
  // We'll monitor console for errors during scroll operations
  const originalError = console.error;
  let errorCount = 0;
  
  console.error = (...args) => {
    errorCount++;
    originalError.apply(console, args);
  };
  
  await testBasicInfiniteScroll();
  
  console.error = originalError;
  
  if (errorCount === 0) {
    console.log('✅ No errors during infinite scroll operations');
    return true;
  } else {
    console.warn(`⚠️ ${errorCount} errors detected during operations`);
    return false;
  }
}

// Main test runner
async function runAllTests() {
  console.log('🧪 Running Comprehensive Infinite Scroll Tests');
  console.log('=' .repeat(50));
  
  const results = {};
  
  if (TESTS.basicScroll) {
    results.basicScroll = await testBasicInfiniteScroll();
    await wait(2000);
  }
  
  if (TESTS.manualButton) {
    results.manualButton = await testManualLoadMore();
    await wait(2000);
  }
  
  if (TESTS.withFilters) {
    results.withFilters = await testWithFilters();
    await wait(2000);
  }
  
  if (TESTS.withSearch) {
    results.withSearch = await testWithSearch();
    await wait(2000);
  }
  
  if (TESTS.withSorting) {
    results.withSorting = await testWithSorting();
    await wait(2000);
  }
  
  if (TESTS.errorHandling) {
    results.errorHandling = await testErrorHandling();
  }
  
  // Summary
  console.log('=' .repeat(50));
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('=' .repeat(50));
  
  const passed = Object.values(results).filter(r => r).length;
  const total = Object.keys(results).length;
  
  Object.entries(results).forEach(([test, result]) => {
    console.log(`${result ? '✅' : '❌'} ${test}: ${result ? 'PASSED' : 'FAILED'}`);
  });
  
  console.log('=' .repeat(50));
  console.log(`🎯 Overall: ${passed}/${total} tests passed (${Math.round(passed/total*100)}%)`);
  
  if (passed === total) {
    console.log('🎉 ALL TESTS PASSED! Infinite scroll is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the logs above for details.');
  }
  
  return results;
}

// Auto-run tests
runAllTests().catch(console.error);

// Export for manual use
window.infiniteScrollTests = {
  runAllTests,
  testBasicInfiniteScroll,
  testManualLoadMore,
  testWithFilters,
  testWithSearch,
  testWithSorting,
  testErrorHandling,
  getEntityCount,
  getPaginationInfo
};

console.log('💡 Tests are running automatically. You can also run individual tests using window.infiniteScrollTests.*');
