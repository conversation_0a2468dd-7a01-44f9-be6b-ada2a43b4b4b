'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { ChatMessage, ChatRequest } from '@/types/chat';
import { postChatMessage, createChatMessage } from '@/services/api';
import ChatWindow from '@/components/chat/ChatWindow';
import ChatInput from '@/components/chat/ChatInput';
import ChatSidebar from '@/components/chat/ChatSidebar';
import LoginPrompt from '@/components/chat/LoginPrompt';
import { Button } from '@/components/ui/button';
import { RefreshCw, MessageCircle, Menu, X, Sparkles } from 'lucide-react';
import {
  chatHistoryService,
  ChatConversation
} from '@/services/chatHistory';

export default function ChatPage() {
  const router = useRouter();
  const { session, user, isLoading: authLoading } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [userInput, setUserInput] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(false);
  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [currentConversation, setCurrentConversation] = useState<ChatConversation | null>(null);

  // Load conversations from localStorage
  useEffect(() => {
    if (session) {
      const loadedConversations = chatHistoryService.getConversations();
      setConversations(loadedConversations);
    }
  }, [session]);

  // Check authentication and redirect if needed
  useEffect(() => {
    if (!authLoading && !session) {
      // User is not authenticated, show login prompt instead of redirecting
      // This allows them to see the chat interface and understand what they're missing
      return;
    }
  }, [authLoading, session]);

  // Initialize chat with welcome message or load existing conversation
  useEffect(() => {
    if (currentConversation && currentConversation.messages.length > 0) {
      // Only load messages from existing conversation if it has messages
      // This prevents clearing messages when a new empty conversation is created
      setMessages(currentConversation.messages);
      setSessionId(currentConversation.id);
    } else if (currentConversation) {
      // New conversation with no messages - just set the session ID
      setSessionId(currentConversation.id);
    }
    // Don't add welcome message here - let WelcomeScreen show instead
  }, [currentConversation]);

  const handleSendMessage = async (messageText?: string) => {
    const textToSend = messageText || userInput.trim();
    if (!textToSend || isLoading) return;

    // Check if user is authenticated
    if (!session) {
      setError('Please log in to start chatting with AI Navigator');
      return;
    }

    // Clear any previous errors
    setError(null);

    // Create user message
    const userMessage = createChatMessage(textToSend, 'user');

    // Ensure we have a current conversation
    let conversationToUse = currentConversation;
    if (!conversationToUse) {
      conversationToUse = chatHistoryService.createNewConversation();
      setCurrentConversation(conversationToUse);
      setSessionId(conversationToUse.id);
      setConversations(prev => [conversationToUse!, ...prev]);
    }

    // If this is the first message, add welcome message first
    const isFirstMessage = messages.length === 0;
    let messagesToAdd = [userMessage];

    if (isFirstMessage) {
      const welcomeMessage = createChatMessage(
        "Hello! I'm your AI Navigator. I can help you find the perfect AI tools, courses, and resources for your needs. What are you looking to accomplish today?",
        'ai'
      );
      // Add welcome message to conversation first
      chatHistoryService.addMessageToConversation(conversationToUse.id, welcomeMessage);
      messagesToAdd = [welcomeMessage, userMessage];
    }

    // Add user message to conversation (only once)
    chatHistoryService.addMessageToConversation(conversationToUse.id, userMessage);

    // Update UI immediately for better UX
    setMessages(prevMessages => [...prevMessages, ...messagesToAdd]);
    const currentInput = textToSend;
    setUserInput('');
    setIsLoading(true);

    try {
      // Prepare chat request according to backend API
      const chatRequest: ChatRequest = {
        message: currentInput,
        session_id: sessionId || undefined,
        user_preferences: {
          technical_level: user?.technicalLevel?.toLowerCase() as 'beginner' | 'intermediate' | 'advanced' || 'intermediate',
          budget: 'medium',
        },
        context: {
          source: 'web',
          page: 'chat',
        },
      };

      // Send to backend
      const response = await postChatMessage(chatRequest, session?.access_token);

      // Update session ID if this is a new conversation
      if (!sessionId && response.session_id) {
        setSessionId(response.session_id);
      }

      // Create AI message from response
      // Convert discovered entities to the format expected by ChatMessage
      const discoveredEntities = response.discovered_entities?.map(entity => ({
        id: entity.id,
        name: entity.name,
        slug: entity.name.toLowerCase().replace(/\s+/g, '-'),
        description: entity.reason,
        websiteUrl: '',
        logoUrl: null,
        entityType: {
          id: '',
          name: 'Tool',
          slug: 'tool',
          description: '',
          iconUrl: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        categories: [],
        tags: [],
        features: [],
        avgRating: 0,
        reviewCount: 0,
        saveCount: 0,
        upvoteCount: 0,
        status: 'ACTIVE',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      })) || [];

      const aiMessage = createChatMessage(
        response.message,
        'ai',
        discoveredEntities
      );

      // Add AI message to conversation
      if (conversationToUse) {
        chatHistoryService.addMessageToConversation(conversationToUse.id, aiMessage);
        // Update conversations list to reflect changes
        setConversations(chatHistoryService.getConversations());
      }

      // Add AI response to messages
      setMessages(prevMessages => [...prevMessages, aiMessage]);
    } catch (error) {
      console.error('Error sending chat message:', error);

      // Create error message
      const errorMessage = createChatMessage(
        "I apologize, but I encountered an error while processing your request. Please try again in a moment.",
        'ai'
      );

      setMessages(prevMessages => [...prevMessages, errorMessage]);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearConversation = () => {
    // Create a new conversation
    const newConversation = chatHistoryService.createNewConversation();
    setCurrentConversation(newConversation);
    setSessionId(newConversation.id);
    setConversations(prev => [newConversation, ...prev]);

    // Clear messages to show WelcomeScreen instead of adding welcome message
    setMessages([]);
    setUserInput('');
    setError(null);
  };

  const handleSelectConversation = (conversationId: string) => {
    const conversation = chatHistoryService.getConversation(conversationId);
    if (conversation) {
      setCurrentConversation(conversation);
      setMessages(conversation.messages);
      setSessionId(conversation.id);
      setError(null);
    }
  };

  const handleDeleteConversation = (conversationId: string) => {
    chatHistoryService.deleteConversation(conversationId);
    setConversations(chatHistoryService.getConversations());

    // If we deleted the current conversation, start a new one
    if (currentConversation?.id === conversationId) {
      handleClearConversation();
    }
  };

  const handleSuggestionClick = (prompt: string) => {
    setUserInput(prompt);
    handleSendMessage(prompt);
  };

  // Show login prompt for unauthenticated users
  if (!authLoading && !session) {
    return <LoginPrompt />;
  }

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading AI Navigator...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      {/* Skip Link for Accessibility */}
      <a
        href="#main-chat"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-indigo-600 text-white px-4 py-2 rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
      >
        Skip to main chat
      </a>

      <div className="flex h-screen">
        {/* Sidebar */}
        <ChatSidebar
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          conversations={conversations}
          currentSessionId={sessionId}
          onSelectConversation={(id) => {
            handleSelectConversation(id);
            setSidebarOpen(false);
          }}
          onNewConversation={handleClearConversation}
          onDeleteConversation={handleDeleteConversation}
        />

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 px-4 py-3 sm:px-6 sm:py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSidebarOpen(true)}
                  className="lg:hidden"
                  aria-label="Open chat history sidebar"
                >
                  <Menu className="w-5 h-5" />
                </Button>

                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                    <Sparkles className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h1 className="text-lg font-semibold text-gray-900">AI Navigator</h1>
                    <p className="text-sm text-gray-500">
                      {user ? `Welcome back, ${user.email?.split('@')[0]}!` : 'Your AI-powered assistant'}
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearConversation}
                  disabled={isLoading}
                  className="flex items-center gap-2 bg-white/50 hover:bg-white/80"
                  aria-label="Start a new conversation"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span className="hidden sm:inline">New Chat</span>
                </Button>
              </div>
            </div>
          </header>

          {/* Error Banner */}
          {error && (
            <div className="mx-4 mt-4 p-3 bg-red-50/80 backdrop-blur-sm border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">
                <strong>Error:</strong> {error}
              </p>
            </div>
          )}

          {/* Chat Window */}
          <main id="main-chat" className="flex-1 overflow-hidden" role="main" aria-label="Chat conversation">
            <ChatWindow
              messages={messages}
              isLoading={isLoading}
              onSuggestionClick={handleSuggestionClick}
            />
          </main>

          {/* Chat Input */}
          <div className="bg-white/80 backdrop-blur-sm border-t border-gray-200/50">
            <ChatInput
              userInput={userInput}
              setUserInput={setUserInput}
              onSubmit={() => handleSendMessage()}
              isLoading={isLoading}
              placeholder="Ask me about AI tools, courses, or anything else..."
              disabled={!session}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
