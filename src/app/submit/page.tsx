'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getEntityTypes, getCategories, getTags, getFeatures, createEntity } from '@/services/api';
import { EntityType, Category, Tag, Feature, CreateEntityDto } from '@/types/entity';
import SubmitForm from '@/components/submit/SubmitForm';

export default function SubmitPage() {
  const router = useRouter();
  const { session, isLoading } = useAuth();

  // State for form selector data
  const [entityTypes, setEntityTypes] = useState<EntityType[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [features, setFeatures] = useState<Feature[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Route protection - redirect if not authenticated
  useEffect(() => {
    if (!isLoading && !session) {
      router.push('/login');
    }
  }, [session, isLoading, router]);

  // Fetch initial data for form selectors
  useEffect(() => {
    const fetchFormData = async () => {
      if (!session?.access_token) return;

      setIsLoadingData(true);
      setError(null);

      try {
        const [typesResult, categoriesResult, tagsResult, featuresResult] = await Promise.all([
          getEntityTypes(session.access_token),
          getCategories(session.access_token),
          getTags(session.access_token),
          getFeatures(session.access_token),
        ]);

        setEntityTypes(typesResult);
        setCategories(categoriesResult);
        setTags(tagsResult);
        setFeatures(featuresResult);
      } catch (err) {
        console.error('Failed to fetch form data:', err);
        setError('Failed to load form data. Please refresh the page and try again.');
      } finally {
        setIsLoadingData(false);
      }
    };

    if (session?.access_token) {
      fetchFormData();
    }
  }, [session?.access_token]);

  // Handle form submission
  const handleSubmit = async (data: CreateEntityDto) => {
    if (!session?.access_token) {
      setError('Authentication required. Please log in and try again.');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      console.log('Submitting entity data:', data);

      const createdEntity = await createEntity(data, session.access_token);
      console.log('Entity created successfully:', createdEntity);

      setSubmitSuccess(true);
      // TODO: Could redirect to the created entity page or reset form
    } catch (err) {
      console.error('Failed to submit entity:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to submit resource. Please try again.';
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!session) {
    return null;
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Submit New Resource</h1>
        <p className="text-gray-600">
          Help grow our community by submitting AI tools, models, datasets, platforms, APIs, libraries, services, or other resources.
          Your submission will be reviewed before being published.
        </p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {submitSuccess && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-green-800">
            Thank you for your contribution! Your submission is now pending review.
          </p>
        </div>
      )}

      {isLoadingData ? (
        <div className="flex items-center justify-center min-h-[300px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading form data...</p>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <SubmitForm
            entityTypes={entityTypes}
            categories={categories}
            tags={tags}
            features={features}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
          />
        </div>
      )}
    </div>
  );
}