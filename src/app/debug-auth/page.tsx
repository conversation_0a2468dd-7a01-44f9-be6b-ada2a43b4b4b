"use client";

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { syncUserProfileAPI, getCompleteProfile } from '@/services/api';

export default function DebugAuthPage() {
  const { user, session, isLoading } = useAuth();
  const adminAuth = useAdminAuth();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [apiResults, setApiResults] = useState<any>({});

  const testSyncProfile = async () => {
    if (!session) {
      setApiResults(prev => ({ ...prev, syncProfile: 'No session available' }));
      return;
    }

    try {
      const result = await syncUserProfileAPI(session);
      setApiResults(prev => ({ ...prev, syncProfile: result }));
    } catch (error) {
      setApiResults(prev => ({ ...prev, syncProfile: `Error: ${error}` }));
    }
  };

  const testGetProfile = async () => {
    if (!session?.access_token) {
      setApiResults(prev => ({ ...prev, getProfile: 'No access token available' }));
      return;
    }

    try {
      const result = await getCompleteProfile(session.access_token);
      setApiResults(prev => ({ ...prev, getProfile: result }));
    } catch (error) {
      setApiResults(prev => ({ ...prev, getProfile: `Error: ${error}` }));
    }
  };

  useEffect(() => {
    setDebugInfo({
      isLoading,
      hasSession: !!session,
      hasUser: !!user,
      userRole: user?.role,
      userEmail: user?.email,
      sessionAccessToken: session?.access_token ? 'Present' : 'Missing',
      adminAuth: {
        isAdmin: adminAuth.isAdmin,
        isModerator: adminAuth.isModerator,
        canManageEntities: adminAuth.canManageEntities,
        isLoading: adminAuth.isLoading,
      },
      supabaseUser: session?.user ? {
        id: session.user.id,
        email: session.user.email,
        app_metadata: session.user.app_metadata,
        user_metadata: session.user.user_metadata,
      } : null,
    });
  }, [user, session, isLoading, adminAuth]);

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-6">Auth Debug Page</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Current State */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Current Auth State</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>

        {/* API Tests */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">API Tests</h2>
          
          <div className="space-y-4">
            <button
              onClick={testSyncProfile}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Test Sync Profile API
            </button>
            
            <button
              onClick={testGetProfile}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              Test Get Profile API
            </button>
          </div>

          <div className="mt-6">
            <h3 className="font-semibold mb-2">API Results:</h3>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
              {JSON.stringify(apiResults, null, 2)}
            </pre>
          </div>
        </div>

        {/* User Object */}
        <div className="bg-white p-6 rounded-lg shadow lg:col-span-2">
          <h2 className="text-xl font-semibold mb-4">Full User Object</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(user, null, 2)}
          </pre>
        </div>
      </div>

      {/* Quick Admin Test */}
      <div className="mt-6 bg-yellow-50 border border-yellow-200 p-4 rounded">
        <h3 className="font-semibold text-yellow-800 mb-2">Quick Admin Test</h3>
        <p className="text-yellow-700">
          Can access admin entities: <strong>{adminAuth.canManageEntities ? 'YES' : 'NO'}</strong>
        </p>
        <p className="text-yellow-700">
          Is Admin: <strong>{adminAuth.isAdmin ? 'YES' : 'NO'}</strong>
        </p>
        <p className="text-yellow-700">
          User Role: <strong>{user?.role || 'No role'}</strong>
        </p>
      </div>
    </div>
  );
}
