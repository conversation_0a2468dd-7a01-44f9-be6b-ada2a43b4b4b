import React from 'react';
import UpvoteTestComponent from '@/components/test/UpvoteTestComponent';

/**
 * Test page for upvote functionality
 * Access this page at /test-upvote to test the upvote system
 * 
 * This page should be removed before production deployment
 */
export default function TestUpvotePage() {
  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Upvote System Test Page
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            This page is for testing the upvote functionality. It includes test components 
            and direct API testing capabilities. Make sure to remove this page before 
            production deployment.
          </p>
        </div>
        
        <UpvoteTestComponent />
        
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            ⚠️ This is a test page - remove before production deployment
          </p>
        </div>
      </div>
    </div>
  );
}
