"use client";

import React from 'react';
import { AdminRouteGuard } from '@/components/admin/AdminRouteGuard';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  FileText, 
  MessageSquare,
  Calendar,
  Activity
} from 'lucide-react';

const StatCard: React.FC<{
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
  icon: React.ComponentType<{ className?: string }>;
}> = ({ title, value, change, changeType, icon: Icon }) => {
  const changeColor = {
    positive: 'text-green-600',
    negative: 'text-red-600',
    neutral: 'text-gray-600',
  };

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Icon className="h-6 w-6 text-gray-400" />
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900">{value}</div>
                <div className={`ml-2 flex items-baseline text-sm font-semibold ${changeColor[changeType]}`}>
                  <TrendingUp className="self-center flex-shrink-0 h-4 w-4" />
                  <span className="ml-1">{change}</span>
                </div>
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function AdminAnalytics() {
  // Mock analytics data
  const analyticsData = {
    totalUsers: { value: '1,247', change: '+12%', changeType: 'positive' as const },
    totalEntities: { value: '856', change: '+8%', changeType: 'positive' as const },
    pendingReviews: { value: '23', change: '-15%', changeType: 'positive' as const },
    toolRequests: { value: '156', change: '+5%', changeType: 'positive' as const },
    monthlyActiveUsers: { value: '892', change: '+18%', changeType: 'positive' as const },
    avgSessionTime: { value: '12m 34s', change: '+3%', changeType: 'positive' as const },
  };

  return (
    <AdminRouteGuard requiredPermission="canViewAnalytics">
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Analytics & Insights</h1>
            <p className="mt-1 text-sm text-gray-500">
              Platform performance metrics and user engagement data
            </p>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
            <StatCard
              title="Total Users"
              value={analyticsData.totalUsers.value}
              change={analyticsData.totalUsers.change}
              changeType={analyticsData.totalUsers.changeType}
              icon={Users}
            />
            <StatCard
              title="Total Entities"
              value={analyticsData.totalEntities.value}
              change={analyticsData.totalEntities.change}
              changeType={analyticsData.totalEntities.changeType}
              icon={FileText}
            />
            <StatCard
              title="Pending Reviews"
              value={analyticsData.pendingReviews.value}
              change={analyticsData.pendingReviews.change}
              changeType={analyticsData.pendingReviews.changeType}
              icon={MessageSquare}
            />
            <StatCard
              title="Tool Requests"
              value={analyticsData.toolRequests.value}
              change={analyticsData.toolRequests.change}
              changeType={analyticsData.toolRequests.changeType}
              icon={MessageSquare}
            />
            <StatCard
              title="Monthly Active Users"
              value={analyticsData.monthlyActiveUsers.value}
              change={analyticsData.monthlyActiveUsers.change}
              changeType={analyticsData.monthlyActiveUsers.changeType}
              icon={Activity}
            />
            <StatCard
              title="Avg Session Time"
              value={analyticsData.avgSessionTime.value}
              change={analyticsData.avgSessionTime.change}
              changeType={analyticsData.avgSessionTime.changeType}
              icon={Calendar}
            />
          </div>

          {/* Charts Placeholder */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">User Growth</h3>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">Chart visualization would go here</p>
                  <p className="text-xs text-gray-400">Integration with charting library needed</p>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Entity Submissions</h3>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">Chart visualization would go here</p>
                  <p className="text-xs text-gray-400">Integration with charting library needed</p>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Popular Categories</h3>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">Chart visualization would go here</p>
                  <p className="text-xs text-gray-400">Integration with charting library needed</p>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">User Engagement</h3>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">Chart visualization would go here</p>
                  <p className="text-xs text-gray-400">Integration with charting library needed</p>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity Summary */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Platform Activity Summary</h3>
            </div>
            <div className="px-6 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-indigo-600">47</div>
                  <div className="text-sm text-gray-500">New entities this week</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">89</div>
                  <div className="text-sm text-gray-500">Reviews submitted</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">156</div>
                  <div className="text-sm text-gray-500">New user registrations</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">23</div>
                  <div className="text-sm text-gray-500">Tool requests pending</div>
                </div>
              </div>
            </div>
          </div>

          {/* Implementation Note */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex">
              <BarChart3 className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-blue-800">
                  Analytics Implementation
                </h3>
                <p className="mt-1 text-sm text-blue-700">
                  This analytics page shows mock data for demonstration. To implement real analytics, 
                  integrate with a charting library like Chart.js, Recharts, or D3.js, and connect 
                  to actual analytics data from your backend API.
                </p>
              </div>
            </div>
          </div>
        </div>
      </AdminLayout>
    </AdminRouteGuard>
  );
}
