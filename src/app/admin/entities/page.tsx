"use client";

import React, { useEffect, useState } from 'react';
import { AdminRouteGuard } from '@/components/admin/AdminRouteGuard';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Trash2, 
  CheckCircle, 
  XCircle, 
  Clock,
  ExternalLink
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from '@/contexts/AuthContext';
import { getEntities, adminUpdateEntityStatus } from '@/services/api';
import { Entity } from '@/types/entity';
import { EntityEditModal } from '@/components/admin/EntityEditModal';
import { EntityDeleteModal } from '@/components/admin/EntityDeleteModal';

interface EntityWithActions extends Entity {
  actions?: string[];
}

const statusColors = {
  PENDING: 'bg-yellow-100 text-yellow-800',
  ACTIVE: 'bg-green-100 text-green-800',
  REJECTED: 'bg-red-100 text-red-800',
  INACTIVE: 'bg-gray-100 text-gray-800',
  ARCHIVED: 'bg-purple-100 text-purple-800',
  NEEDS_REVISION: 'bg-orange-100 text-orange-800',
};

const statusIcons = {
  PENDING: Clock,
  ACTIVE: CheckCircle,
  REJECTED: XCircle,
  INACTIVE: XCircle,
  ARCHIVED: XCircle,
  NEEDS_REVISION: Clock,
};

export default function EntitiesManagement() {
  const { session } = useAuth();
  const [entities, setEntities] = useState<EntityWithActions[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [updating, setUpdating] = useState<string | null>(null);

  // Modal states
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedEntity, setSelectedEntity] = useState<Entity | null>(null);

  const fetchEntities = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: 20,
        ...(searchTerm && { searchTerm }),
        ...(statusFilter && statusFilter !== 'all' && { status: statusFilter as "REJECTED" | "PENDING" | "ACTIVE" | "INACTIVE" | "ARCHIVED" | "NEEDS_REVISION" }),
        ...(typeFilter && typeFilter !== 'all' && { entityTypeIds: [typeFilter] }),
      };

      const response = await getEntities(params, session?.access_token);
      setEntities(response.data || []);
      setTotalPages(response.meta?.totalPages || 1);
    } catch (error) {
      console.error('Error fetching entities:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (session) {
      fetchEntities();
    }
  }, [session, currentPage, searchTerm, statusFilter, typeFilter]);

  const handleStatusUpdate = async (entityId: string, newStatus: string) => {
    if (!session?.access_token) return;

    try {
      setUpdating(entityId);
      await adminUpdateEntityStatus(entityId, newStatus, session.access_token);

      // Update local state
      setEntities(prev => prev.map(entity =>
        entity.id === entityId
          ? { ...entity, status: newStatus as any }
          : entity
      ));
    } catch (error) {
      console.error('Error updating entity status:', error);
      alert('Failed to update entity status');
    } finally {
      setUpdating(null);
    }
  };

  const handleEditEntity = (entity: Entity) => {
    setSelectedEntity(entity);
    setEditModalOpen(true);
  };

  const handleDeleteEntity = (entity: Entity) => {
    setSelectedEntity(entity);
    setDeleteModalOpen(true);
  };

  const handleEditSuccess = (updatedEntity: Entity) => {
    // Update local state with the updated entity
    setEntities(prev => prev.map(entity =>
      entity.id === updatedEntity.id ? updatedEntity : entity
    ));
  };

  const handleDeleteSuccess = () => {
    // Remove the deleted entity from local state
    if (selectedEntity) {
      setEntities(prev => prev.filter(entity => entity.id !== selectedEntity.id));
    }
  };

  const EntityRow: React.FC<{ entity: EntityWithActions }> = ({ entity }) => {
    const StatusIcon = statusIcons[entity.status as keyof typeof statusIcons] || Clock;

    return (
      <tr className="hover:bg-gray-50">
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="flex items-center">
            <div className="flex-shrink-0 h-10 w-10">
              {entity.logoUrl ? (
                <img 
                  className="h-10 w-10 rounded-lg object-cover" 
                  src={entity.logoUrl} 
                  alt={entity.name}
                />
              ) : (
                <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                  <span className="text-sm font-medium text-gray-500">
                    {entity.name.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-900">{entity.name}</div>
              <div className="text-sm text-gray-500">{entity.entityType?.name}</div>
            </div>
          </div>
        </td>
        
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="text-sm text-gray-900">{entity.shortDescription}</div>
        </td>
        
        <td className="px-6 py-4 whitespace-nowrap">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            statusColors[entity.status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'
          }`}>
            <StatusIcon className="w-3 h-3 mr-1" />
            {entity.status}
          </span>
        </td>
        
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          {new Date(entity.createdAt).toLocaleDateString()}
        </td>
        
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          <div className="flex items-center space-x-2">
            <span>⭐ {entity.avgRating?.toFixed(1) || '0.0'}</span>
            <span>👍 {entity.upvoteCount || 0}</span>
          </div>
        </td>
        
        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.open(entity.websiteUrl, '_blank')}
              disabled={!entity.websiteUrl}
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" disabled={updating === entity.id}>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => window.open(`/entities/${entity.slug}`, '_blank')}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>

                <DropdownMenuItem onClick={() => handleEditEntity(entity)}>
                  <Edit className="mr-2 h-4 w-4 text-blue-600" />
                  Edit Entity
                </DropdownMenuItem>

                {entity.status === 'PENDING' && (
                  <>
                    <DropdownMenuItem onClick={() => handleStatusUpdate(entity.id, 'ACTIVE')}>
                      <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                      Approve
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleStatusUpdate(entity.id, 'REJECTED')}>
                      <XCircle className="mr-2 h-4 w-4 text-red-600" />
                      Reject
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleStatusUpdate(entity.id, 'NEEDS_REVISION')}>
                      <Edit className="mr-2 h-4 w-4 text-orange-600" />
                      Needs Revision
                    </DropdownMenuItem>
                  </>
                )}

                {entity.status === 'ACTIVE' && (
                  <>
                    <DropdownMenuItem onClick={() => handleStatusUpdate(entity.id, 'INACTIVE')}>
                      <XCircle className="mr-2 h-4 w-4 text-gray-600" />
                      Deactivate
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleStatusUpdate(entity.id, 'ARCHIVED')}>
                      <Trash2 className="mr-2 h-4 w-4 text-purple-600" />
                      Archive
                    </DropdownMenuItem>
                  </>
                )}

                {(entity.status === 'REJECTED' || entity.status === 'INACTIVE') && (
                  <DropdownMenuItem onClick={() => handleStatusUpdate(entity.id, 'ACTIVE')}>
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    Reactivate
                  </DropdownMenuItem>
                )}

                <DropdownMenuItem
                  onClick={() => handleDeleteEntity(entity)}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Entity
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </td>
      </tr>
    );
  };

  return (
    <AdminRouteGuard requiredPermission="canManageEntities">
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Entity Management</h1>
              <p className="mt-1 text-sm text-gray-500">
                Manage and moderate all entities in the platform
              </p>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search entities..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="REJECTED">Rejected</SelectItem>
                  <SelectItem value="INACTIVE">Inactive</SelectItem>
                  <SelectItem value="ARCHIVED">Archived</SelectItem>
                  <SelectItem value="NEEDS_REVISION">Needs Revision</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="tool">AI Tools</SelectItem>
                  <SelectItem value="course">Courses</SelectItem>
                  <SelectItem value="job">Jobs</SelectItem>
                  <SelectItem value="hardware">Hardware</SelectItem>
                  <SelectItem value="event">Events</SelectItem>
                </SelectContent>
              </Select>
              
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setTypeFilter('all');
                }}
              >
                <Filter className="h-4 w-4 mr-2" />
                Clear Filters
              </Button>
            </div>
          </div>

          {/* Entities Table */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Entity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Metrics
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {loading ? (
                    <tr>
                      <td colSpan={6} className="px-6 py-12 text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
                        <p className="mt-2 text-sm text-gray-500">Loading entities...</p>
                      </td>
                    </tr>
                  ) : entities.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="px-6 py-12 text-center">
                        <p className="text-sm text-gray-500">No entities found</p>
                      </td>
                    </tr>
                  ) : (
                    entities.map((entity) => (
                      <EntityRow key={entity.id} entity={entity} />
                    ))
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div className="flex-1 flex justify-between sm:hidden">
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      Page <span className="font-medium">{currentPage}</span> of{' '}
                      <span className="font-medium">{totalPages}</span>
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                      <Button
                        variant="outline"
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                        className="rounded-r-none"
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPage === totalPages}
                        className="rounded-l-none"
                      >
                        Next
                      </Button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Edit Modal */}
          <EntityEditModal
            entity={selectedEntity}
            isOpen={editModalOpen}
            onClose={() => {
              setEditModalOpen(false);
              setSelectedEntity(null);
            }}
            onSuccess={handleEditSuccess}
          />

          {/* Delete Modal */}
          <EntityDeleteModal
            entity={selectedEntity}
            isOpen={deleteModalOpen}
            onClose={() => {
              setDeleteModalOpen(false);
              setSelectedEntity(null);
            }}
            onSuccess={handleDeleteSuccess}
          />
        </div>
      </AdminLayout>
    </AdminRouteGuard>
  );
}
