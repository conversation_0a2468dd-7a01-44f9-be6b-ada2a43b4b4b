"use client";

import React from 'react';
import { AdminRouteGuard } from '@/components/admin/AdminRouteGuard';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';

export default function AdminTest() {
  const adminAuth = useAdminAuth();

  const TestItem: React.FC<{ 
    label: string; 
    value: boolean | string | null; 
    type?: 'boolean' | 'string' 
  }> = ({ label, value, type = 'boolean' }) => {
    if (type === 'boolean') {
      return (
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <span className="text-sm font-medium text-gray-700">{label}</span>
          <div className="flex items-center">
            {value ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <XCircle className="h-5 w-5 text-red-600" />
            )}
            <span className="ml-2 text-sm text-gray-600">
              {value ? 'Yes' : 'No'}
            </span>
          </div>
        </div>
      );
    }

    return (
      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
        <span className="text-sm font-medium text-gray-700">{label}</span>
        <span className="text-sm text-gray-600">
          {value || 'Not available'}
        </span>
      </div>
    );
  };

  return (
    <AdminRouteGuard requiredPermission="isAdminOrModerator">
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Admin Panel Test</h1>
            <p className="mt-1 text-sm text-gray-500">
              Test page to verify admin authentication and permissions
            </p>
          </div>

          {/* Authentication Status */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Authentication Status</h3>
            <div className="space-y-3">
              <TestItem label="Is Loading" value={adminAuth.isLoading} />
              <TestItem label="User Authenticated" value={!!adminAuth.user} />
              <TestItem label="User Email" value={adminAuth.user?.email} type="string" />
              <TestItem label="User Role" value={adminAuth.user?.role} type="string" />
            </div>
          </div>

          {/* Permissions */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Permissions</h3>
            <div className="space-y-3">
              <TestItem label="Is Admin" value={adminAuth.isAdmin} />
              <TestItem label="Is Moderator" value={adminAuth.isModerator} />
              <TestItem label="Is Admin or Moderator" value={adminAuth.isAdminOrModerator} />
              <TestItem label="Can Manage Users" value={adminAuth.canManageUsers} />
              <TestItem label="Can Manage Entities" value={adminAuth.canManageEntities} />
              <TestItem label="Can Manage Settings" value={adminAuth.canManageSettings} />
              <TestItem label="Can View Analytics" value={adminAuth.canViewAnalytics} />
            </div>
          </div>

          {/* Status Summary */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Status Summary</h3>
            <div className="space-y-4">
              {adminAuth.isAdminOrModerator ? (
                <div className="flex items-center p-4 bg-green-50 border border-green-200 rounded-lg">
                  <CheckCircle className="h-6 w-6 text-green-600 mr-3" />
                  <div>
                    <h4 className="text-sm font-medium text-green-800">
                      Admin Access Granted
                    </h4>
                    <p className="text-sm text-green-700">
                      You have {adminAuth.isAdmin ? 'administrator' : 'moderator'} privileges and can access the admin panel.
                    </p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center p-4 bg-red-50 border border-red-200 rounded-lg">
                  <XCircle className="h-6 w-6 text-red-600 mr-3" />
                  <div>
                    <h4 className="text-sm font-medium text-red-800">
                      Access Denied
                    </h4>
                    <p className="text-sm text-red-700">
                      You do not have admin or moderator privileges.
                    </p>
                  </div>
                </div>
              )}

              {adminAuth.user && (
                <div className="flex items-center p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <AlertCircle className="h-6 w-6 text-blue-600 mr-3" />
                  <div>
                    <h4 className="text-sm font-medium text-blue-800">
                      Available Features
                    </h4>
                    <ul className="text-sm text-blue-700 mt-1 space-y-1">
                      {adminAuth.canViewAnalytics && <li>• View Analytics Dashboard</li>}
                      {adminAuth.canManageEntities && <li>• Manage Entities and Content</li>}
                      {adminAuth.canManageUsers && <li>• Manage Users and Roles</li>}
                      {adminAuth.canManageSettings && <li>• Manage System Settings</li>}
                    </ul>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Debug Information */}
          {process.env.NODE_ENV === 'development' && (
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Debug Information</h3>
              <pre className="text-xs bg-gray-100 p-4 rounded-lg overflow-auto">
                {JSON.stringify(adminAuth, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </AdminLayout>
    </AdminRouteGuard>
  );
}
