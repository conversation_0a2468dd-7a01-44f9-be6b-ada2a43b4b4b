import { UserR<PERSON>, UserStatus, TechnicalLevel } from './enums';

export interface PrismaUserProfile {
  id: string;
  authUserId: string; // This usually refers to the Supabase auth user ID (camelCase from backend)
  username: string | null;
  displayName: string | null; // camelCase from backend
  email: string; // Typically unique and matches Supabase auth email
  role: UserRole;
  status: UserStatus;
  technicalLevel: TechnicalLevel | null; // camelCase from backend
  profilePictureUrl: string | null; // camelCase from backend
  bio: string | null;
  socialLinks: Record<string, string> | null; // camelCase from backend
  createdAt: string; // ISO date string (camelCase from backend)
  updatedAt: string; // ISO date string (camelCase from backend)
  lastLogin: string | null; // ISO date string or null (camelCase from backend)
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData extends LoginCredentials {
  displayName?: string; // Optional display name during registration
  // Add any other registration fields if necessary
} 