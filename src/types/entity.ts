export interface EntityType {
  id: string;
  name: string;
  slug: string;
  description: string;
  iconUrl?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
  iconUrl?: string | null;
  parentCategoryId?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Tag {
  id: string;
  name: string;
  slug: string;
}

export interface Feature {
  id: string;
  name: string;
  slug: string;
  description: string;
  iconUrl?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface SocialLinks {
  twitter?: string;
  linkedin?: string;
  github?: string;
  youtube?: string;
  facebook?: string;
  instagram?: string;
  discord?: string;
  [key: string]: string | undefined;
}

export interface Submitter {
  id: string;
  email: string;
  created_at: string;
  last_sign_in_at?: string | null;
  user_metadata: {
    username?: string | null;
    display_name?: string | null;
    profile_picture_url?: string | null;
    internal_user_id?: string;
  };
}

// Updated details interface based on actual API response
export interface EntityDetails {
  entityId: string;
  programmingLanguages?: string[] | null;
  frameworks?: string[] | null;
  libraries?: string[] | null;
  integrations?: string[] | null;
  keyFeatures?: string[];
  useCases?: string[];
  targetAudience?: string[];
  learningCurve?: 'LOW' | 'MEDIUM' | 'HIGH' | null;
  deploymentOptions?: string[] | null;
  supportedOs?: string[] | null;
  mobileSupport?: boolean;
  apiAccess?: boolean;
  customizationLevel?: string | null;
  trialAvailable?: boolean;
  demoAvailable?: boolean;
  openSource?: boolean;
  supportChannels?: string[];
  hasFreeTier?: boolean;
  pricingModel?: 'FREE' | 'FREEMIUM' | 'PAID' | 'SUBSCRIPTION' | 'ONE_TIME' | null;
  priceRange?: 'FREE' | 'LOW' | 'MEDIUM' | 'HIGH' | 'ENTERPRISE' | null;
  pricingDetails?: string | null;
  pricingUrl?: string | null;
  supportEmail?: string | null;
  hasLiveChat?: boolean | null;
  communityUrl?: string | null;
  [key: string]: unknown; // Allow other dynamic fields
}

// Legacy detail interfaces for backward compatibility
export interface ToolDetails {
  technical_level?: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert' | string;
  key_features?: string[];
  integrations?: string[];
  use_cases?: string[];
  pricing_model?: 'Free' | 'Freemium' | 'Paid' | 'Subscription' | 'One-time Purchase' | string;
  api_available?: boolean;
  self_hosted_option?: boolean;
  [key: string]: unknown;
}

export interface CourseDetails {
  instructor_name?: string;
  duration_text?: string;
  skill_level?: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | string; // Updated field name and values
  certificate_available?: boolean;
  prerequisites?: string[];
  learning_outcomes?: string[];
  language?: string;
  syllabus_url?: string; // New field
  enrollment_count?: number; // New field
  [key: string]: unknown;
}

export interface AgencyDetails {
  services_offered?: string[];
  portfolio_url?: string;
  team_size?: number | string;
  specializations?: string[];
  contact_email?: string;
  region_served?: string;
  location_summary?: string; // New field
  pricing_info?: string; // New field
  industry_focus?: string[]; // New field
  target_client_size?: string[]; // New field
  [key: string]: unknown;
}

export interface ContentCreatorDetails {
  platform?: 'YouTube' | 'TikTok' | 'Twitch' | 'Instagram' | 'Blog' | 'Podcast' | string;
  platform_url?: string;
  subscriber_count?: string;
  content_focus?: string[];
  collaboration_email?: string;
  sample_work_links?: string[];
  [key: string]: unknown;
}

export interface CommunityDetails {
  platform_name?: string;
  platform_url?: string;
  member_count?: string;
  main_topics?: string[];
  moderator_info?: string;
  entry_requirements?: string;
  [key: string]: unknown;
}

export interface NewsletterDetails {
  frequency?: string; // Updated field name
  target_audience?: string;
  archive_url?: string;
  subscribe_url?: string; // Updated field name
  author_name?: string;
  subscriber_count?: number;
  main_topics?: string[]; // Updated field name
  [key: string]: unknown;
}

// New entity detail interfaces for enhanced forms
export interface SoftwareDetails {
  current_version?: string;
  license_type?: string;
  community_url?: string;
  has_free_tier?: boolean;
  has_live_chat?: boolean;
  price_range?: 'FREE' | 'LOW' | 'MEDIUM' | 'HIGH' | 'ENTERPRISE';
  pricing_details?: string;
  pricing_model?: 'FREE' | 'FREEMIUM' | 'PAID' | 'SUBSCRIPTION' | 'ONE_TIME';
  pricing_url?: string;
  support_email?: string;
  api_access?: boolean;
  customization_level?: string;
  demo_available?: boolean;
  deployment_options?: string[];
  frameworks?: string[];
  has_api?: boolean;
  key_features?: string[];
  libraries?: string[];
  mobile_support?: boolean;
  open_source?: boolean;
  support_channels?: string[];
  supported_os?: string[];
  target_audience?: string[];
  trial_available?: boolean;
  integrations?: string[];
  platform_compatibility?: string[];
  programming_languages?: string[];
  use_cases?: string[];
  [key: string]: unknown;
}

export interface ModelDetails {
  model_architecture?: string;
  training_dataset?: string;
  license?: string;
  performance_metrics?: Record<string, string | number | boolean>;
  input_data_types?: string[];
  output_data_types?: string[];
  frameworks?: string[];
  libraries?: string[];
  deployment_options?: string[];
  use_cases?: string[];
  target_audience?: string[];
  [key: string]: unknown;
}

export interface DatasetDetails {
  license?: string;
  size_in_bytes?: number;
  format?: string;
  source_url?: string;
  collection_method?: string;
  update_frequency?: string;
  access_notes?: string;
  description?: string;
  [key: string]: unknown;
}

export interface PlatformDetails {
  platform_type?: string;
  documentation_url?: string;
  community_url?: string;
  pricing_model?: 'FREE' | 'FREEMIUM' | 'PAID' | 'SUBSCRIPTION' | 'ONE_TIME';
  price_range?: 'FREE' | 'LOW' | 'MEDIUM' | 'HIGH' | 'ENTERPRISE';
  has_free_tier?: boolean;
  has_api?: boolean;
  has_live_chat?: boolean;
  api_access?: boolean;
  demo_available?: boolean;
  trial_available?: boolean;
  mobile_support?: boolean;
  customization_level?: string;
  pricing_url?: string;
  support_email?: string;
  pricing_details?: string;
  deployment_options?: string[];
  target_audience?: string[];
  supported_os?: string[];
  integrations?: string[];
  key_services?: string[];
  use_cases?: string[];
  [key: string]: unknown;
}

// Additional entity detail interfaces for comprehensive entity types
export interface JobDetails {
  company_name?: string;
  employment_types?: string[];
  experience_level?: string;
  location_types?: string[];
  salary_min?: number;
  salary_max?: number;
  job_description?: string;
  application_url?: string;
  is_remote?: boolean;
  location?: string;
  job_type?: string;
  required_skills?: string[];
  key_responsibilities?: string[];
  [key: string]: unknown;
}

export interface EventDetails {
  event_types?: string[];
  start_date?: string;
  end_date?: string;
  is_online?: boolean;
  location?: string;
  price_text?: string;
  registration_required?: boolean;
  registration_url?: string;
  key_speakers?: string[];
  topics?: string[];
  target_audience?: string[];
  [key: string]: unknown;
}

export interface HardwareDetails {
  hardware_type?: string;
  manufacturer?: string;
  release_date?: string;
  price_range?: string;
  price_min?: number;
  price_max?: number;
  specifications?: Record<string, any>;
  datasheet_url?: string;
  memory?: string;
  processor?: string;
  [key: string]: unknown;
}

export interface ResearchPaperDetails {
  research_areas?: string[];
  authors?: string[];
  publication_date?: string;
  publication_venue?: string;
  keywords?: string[];
  abstract?: string;
  doi?: string;
  arxiv_id?: string;
  [key: string]: unknown;
}

export interface BookDetails {
  author_name?: string;
  isbn?: string;
  formats?: string[];
  publication_date?: string;
  publisher?: string;
  page_count?: number;
  language?: string;
  [key: string]: unknown;
}

export interface PodcastDetails {
  hosts?: string[];
  topics?: string[];
  platforms?: string[];
  frequency?: string;
  episode_count?: number;
  average_duration?: string;
  [key: string]: unknown;
}

export interface GrantDetails {
  funding_amount_min?: number;
  funding_amount_max?: number;
  application_deadline?: string;
  eligibility_criteria?: string[];
  funding_areas?: string[];
  application_url?: string;
  contact_email?: string;
  [key: string]: unknown;
}

export interface Entity {
  id: string;
  name: string;
  slug: string;
  description: string;
  shortDescription?: string;
  websiteUrl: string;
  logoUrl: string | null;
  documentationUrl?: string | null;
  contactUrl?: string | null;
  privacyPolicyUrl?: string | null;
  foundedYear?: number | null;
  entityType: EntityType;
  categories: Category[];
  tags: Tag[];
  features: Feature[];
  avgRating: number;
  reviewCount: number;
  saveCount: number;
  upvoteCount?: number; // Added for upvote functionality - optional until backend provides it
  status: string; // e.g., 'ACTIVE', 'PENDING'
  socialLinks?: SocialLinks | null;
  submitter?: Submitter;
  legacyId?: string | null;
  createdAt: string;
  updatedAt: string;
  metaTitle?: string;
  metaDescription?: string;
  scrapedReviewSentimentLabel?: string | null;
  scrapedReviewSentimentScore?: number | null;
  scrapedReviewCount?: number | null;
  employeeCountRange?: string | null;
  fundingStage?: string | null;
  locationSummary?: string | null;
  refLink?: string;
  affiliateStatus?: string;
  hasFreeTier?: boolean;
  details?: EntityDetails;
}

export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalItems: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface PaginatedEntities {
  data: Entity[];
  meta: PaginationMeta;
}

// Interface for query parameters for fetching entities
export interface GetEntitiesParams {
  page?: number;
  limit?: number;
  searchTerm?: string;
  categoryIds?: string[];
  tagIds?: string[];
  entityTypeIds?: string[];
  entityTypeId?: string; // Single entity type filter
  featureIds?: string[];
  sortBy?: 'createdAt' | 'updatedAt' | 'name' | 'reviewCount' | 'averageRating' | 'saveCount' | 'viewCount' | 'popularity';
  sortOrder?: 'asc' | 'desc';
  submitterId?: string;

  // Date filters
  createdAtFrom?: string;
  createdAtTo?: string;

  // Boolean filters
  hasFreeTier?: boolean;
  apiAccess?: boolean;

  // Array filters
  employeeCountRanges?: string[];
  fundingStages?: string[];
  pricingModels?: string[];
  priceRanges?: string[];
  integrations?: string[];
  platforms?: string[];
  targetAudience?: string[];

  // Location search
  locationSearch?: string;

  // Rating filters
  rating_min?: number;
  rating_max?: number;

  // Review count filters
  review_count_min?: number;
  review_count_max?: number;

  // Affiliate filters
  affiliate_status?: 'NONE' | 'APPLIED' | 'APPROVED' | 'REJECTED';
  affiliateStatus?: string[]; // Array version for multiple affiliate statuses
  has_affiliate_link?: boolean;
  has_api_documentation?: boolean;

  // Status & Moderation filters
  status?: 'PENDING' | 'ACTIVE' | 'REJECTED' | 'INACTIVE' | 'ARCHIVED' | 'NEEDS_REVISION';

  // ✅ COMPREHENSIVE ENTITY-SPECIFIC FILTERS (FLAT PARAMETERS) 🎉

  // Tool/AI Tool filters
  technical_levels?: string[];
  learning_curves?: string[];
  has_api?: boolean;
  has_free_tier?: boolean;
  open_source?: boolean;
  mobile_support?: boolean;
  demo_available?: boolean;
  has_live_chat?: boolean;
  frameworks?: string[];
  libraries?: string[];
  deployment_options?: string[];
  support_channels?: string[];
  key_features_search?: string;
  use_cases_search?: string;
  target_audience_search?: string;
  customization_level?: string;
  pricing_details_search?: string;

  // Course filters
  skill_levels?: string[];
  certificate_available?: boolean;
  instructor_name?: string;
  duration_text?: string;
  enrollment_min?: number;
  enrollment_max?: number;
  prerequisites?: string;
  has_syllabus?: boolean;

  // Job filters
  employment_types?: string[];
  experience_levels?: string[];
  location_types?: string[];
  company_name?: string;
  job_title?: string;
  salary_min?: number;
  salary_max?: number;
  has_application_url?: boolean;

  // Hardware filters
  hardware_types?: string[];
  manufacturers?: string[];
  price_range?: string;
  price_min?: number;
  price_max?: number;
  memory_search?: string;
  processor_search?: string;
  release_date_from?: string;
  release_date_to?: string;
  has_datasheet?: boolean;

  // Event filters
  event_types?: string[];
  start_date_from?: string;
  start_date_to?: string;
  end_date_from?: string;
  end_date_to?: string;
  is_online?: boolean;
  location?: string;
  price_text?: string;
  has_registration_url?: boolean;
  speakers_search?: string;

  // Agency filters
  services_offered?: string[];
  industry_focus?: string[];
  has_portfolio?: boolean;

  // Software filters
  license_types?: string[];
  programming_languages?: string[];
  platform_compatibility?: string[];
  current_version?: string;
  has_repository?: boolean;

  // Research Paper filters
  research_areas?: string[];
  authors_search?: string;
  publication_date_from?: string;
  publication_date_to?: string;

  // Book filters
  author_name?: string;
  isbn?: string;
  formats?: string[];
}

// Interface for creating a new entity (mirrors backend CreateEntityDto)
export interface CreateEntityDto {
  // Core entity information
  name: string;
  short_description?: string;
  description: string;
  website_url: string;
  logo_url?: string;
  documentation_url?: string;
  contact_url?: string;
  privacy_policy_url?: string;
  founded_year?: number;

  // Entity type and categorization
  entity_type_id: string;
  category_ids?: string[];
  tag_ids?: string[];
  feature_ids?: string[];

  // Social links
  social_links?: SocialLinks;

  // SEO metadata
  meta_title?: string;
  meta_description?: string;

  // Company/organization details
  employee_count_range?: string;
  funding_stage?: string;
  location_summary?: string;

  // Referral and affiliate
  ref_link?: string;
  affiliate_status?: string;

  // Type-specific details (dynamic based on entity type)
  details?: EntityDetails | ToolDetails | CourseDetails | AgencyDetails | ContentCreatorDetails | CommunityDetails | NewsletterDetails;

  // Enhanced entity detail types
  tool_details?: ToolDetails;
  course_details?: CourseDetails;
  agency_details?: AgencyDetails;
  newsletter_details?: NewsletterDetails;
  software_details?: SoftwareDetails;
  model_details?: ModelDetails;
  dataset_details?: DatasetDetails;
  platform_details?: PlatformDetails;
  community_details?: CommunityDetails;
  content_creator_details?: ContentCreatorDetails;
  job_details?: JobDetails;
  event_details?: EventDetails;
  hardware_details?: HardwareDetails;
  research_paper_details?: ResearchPaperDetails;
  book_details?: BookDetails;
  podcast_details?: PodcastDetails;
  grant_details?: GrantDetails;
}

// Interface for updating an entity (mirrors backend UpdateEntityDto)
export interface UpdateEntityDto {
  // Core entity information
  name?: string;
  short_description?: string;
  description?: string;
  website_url?: string;
  logo_url?: string;
  documentation_url?: string;
  contact_url?: string;
  privacy_policy_url?: string;
  founded_year?: number;

  // Entity type and categorization
  entity_type_id?: string;
  category_ids?: string[];
  tag_ids?: string[];
  feature_ids?: string[];

  // Social links
  social_links?: SocialLinks;

  // SEO metadata
  meta_title?: string;
  meta_description?: string;

  // Company/organization details
  employee_count_range?: string;
  funding_stage?: string;
  location_summary?: string;

  // Referral and affiliate
  ref_link?: string;
  affiliate_status?: string;

  // Status (for admin updates)
  status?: 'PENDING' | 'ACTIVE' | 'REJECTED' | 'INACTIVE' | 'ARCHIVED' | 'NEEDS_REVISION';

  // Additional fields
  has_live_chat?: boolean;
  scraped_review_sentiment_label?: string;
  scraped_review_sentiment_score?: number;
  scraped_review_count?: number;

  // Type-specific details (dynamic based on entity type)
  tool_details?: ToolDetails;
  course_details?: CourseDetails;
  agency_details?: AgencyDetails;
  newsletter_details?: NewsletterDetails;
  software_details?: SoftwareDetails;
  model_details?: ModelDetails;
  dataset_details?: DatasetDetails;
  platform_details?: PlatformDetails;
  community_details?: CommunityDetails;
  content_creator_details?: ContentCreatorDetails;
  job_details?: JobDetails;
  event_details?: EventDetails;
  hardware_details?: HardwareDetails;
  research_paper_details?: ResearchPaperDetails;
  book_details?: BookDetails;
  podcast_details?: PodcastDetails;
  grant_details?: GrantDetails;
}