import { Entity } from './entity';

/**
 * Represents a single message in the chat conversation
 */
export interface ChatMessage {
  /** Unique identifier for the message */
  id: string;
  /** The text content of the message */
  text: string;
  /** Who sent the message - user or AI */
  sender: 'user' | 'ai';
  /** Timestamp when the message was created */
  timestamp: Date;
  /** Optional recommended entities if the AI message includes recommendations */
  recommendedEntities?: Entity[];
  /** Optional metadata for the message */
  metadata?: {
    /** Whether this message is still being processed */
    isLoading?: boolean;
    /** Any error that occurred while processing this message */
    error?: string;
    /** Additional context or data */
    [key: string]: unknown;
  };
}

/**
 * Request payload for the chat API endpoint (matches backend SendChatMessageDto)
 */
export interface ChatRequest {
  /** The user message to send to the chatbot */
  message: string;
  /** Optional session ID to continue an existing conversation */
  session_id?: string;
  /** User preferences to help personalize the conversation */
  user_preferences?: {
    budget?: 'free' | 'low' | 'medium' | 'high';
    technical_level?: 'beginner' | 'intermediate' | 'advanced';
    preferred_categories?: string[];
    excluded_categories?: string[];
  };
  /** Additional context or metadata for the message */
  context?: {
    source?: string;
    page?: string;
    [key: string]: unknown;
  };
}

/**
 * Response from the chat API endpoint (matches backend ChatResponseDto)
 */
export interface ChatResponse {
  /** The chatbot response message */
  message: string;
  /** Session ID for this conversation */
  session_id: string;
  /** Current stage of the conversation */
  conversation_stage: 'greeting' | 'discovery' | 'refinement' | 'recommendation' | 'comparison';
  /** Suggested actions the user can take */
  suggested_actions?: Array<{
    type: 'ask_question' | 'show_entities' | 'refine_search' | 'get_recommendations';
    label: string;
    data?: Record<string, unknown>;
  }>;
  /** AI tools discovered during this conversation turn */
  discovered_entities?: Array<{
    id: string;
    name: string;
    relevance_score: number;
    reason: string;
  }>;
  /** Follow-up questions to guide the conversation */
  follow_up_questions?: string[];
  /** Whether the conversation is ready to transition to formal recommendations */
  should_transition_to_recommendations: boolean;
  /** Response metadata including timing and provider information */
  metadata: {
    response_time: number;
    llm_provider: string;
    tokens_used?: number;
  };
  /** Timestamp when the response was generated */
  generated_at: string;
}

/**
 * Error response from the chat API
 */
export interface ChatError {
  /** Error message */
  message: string;
  /** Error code for programmatic handling */
  code?: string;
  /** Additional error details */
  details?: string;
  /** HTTP status code */
  statusCode?: number;
}

/**
 * State interface for managing the chat conversation
 */
export interface ChatState {
  /** Array of all messages in the conversation */
  messages: ChatMessage[];
  /** Current user input */
  userInput: string;
  /** Whether a request is currently being processed */
  isLoading: boolean;
  /** Any error that occurred */
  error: string | null;
  /** Whether the chat has been initialized */
  isInitialized: boolean;
}

/**
 * Actions for updating chat state
 */
export type ChatAction =
  | { type: 'SET_USER_INPUT'; payload: string }
  | { type: 'ADD_MESSAGE'; payload: ChatMessage }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'INITIALIZE_CHAT' }
  | { type: 'CLEAR_CONVERSATION' };

/**
 * Utility type for creating a new chat message
 */
export interface CreateMessageOptions {
  text: string;
  sender: 'user' | 'ai';
  recommendedEntities?: Entity[];
  metadata?: ChatMessage['metadata'];
}
