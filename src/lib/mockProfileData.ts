import { CompleteProfileData, UserPreferences, ProfileStats, ProfileActivity, ToolRequest, UserSubmittedTool } from '@/types/profile';
import { PrismaUserProfile } from '@/types/user';
import { UserRole, UserStatus, TechnicalLevel } from '@/types/enums';

// Mock user profile data for development/testing
export const mockUserProfile: PrismaUserProfile = {
  id: 'user-123',
  authUserId: 'auth-123',
  username: 'johndoe',
  displayName: '<PERSON>',
  email: '<EMAIL>',
  role: UserRole.USER,
  status: UserStatus.ACTIVE,
  technicalLevel: TechnicalLevel.INTERMEDIATE,
  profilePictureUrl: null,
  bio: 'AI enthusiast and developer passionate about exploring new tools and technologies.',
  socialLinks: {
    website: 'https://johndoe.dev',
    twitter: 'https://twitter.com/johndoe',
    linkedin: 'https://linkedin.com/in/johndoe',
    github: 'https://github.com/johndoe'
  },
  createdAt: '2023-01-15T10:00:00Z',
  updatedAt: '2024-01-15T10:00:00Z',
  lastLogin: '2024-01-15T10:00:00Z'
};

export const mockUserPreferences: UserPreferences = {
  id: 'pref-123',
  user_id: 'user-123',
  email_notifications: true,
  marketing_emails: false,
  weekly_digest: true,
  new_tool_alerts: true,
  profile_visibility: 'public',
  show_bookmarks: true,
  show_reviews: true,
  show_activity: true,
  theme: 'light',
  items_per_page: 20,
  default_view: 'grid',
  preferred_categories: ['AI Tools', 'Development', 'Productivity'],
  blocked_categories: [],
  content_language: 'en',
  created_at: '2023-01-15T10:00:00Z',
  updated_at: '2024-01-15T10:00:00Z'
};

export const mockProfileStats: ProfileStats = {
  bookmarks_count: 24,
  reviews_count: 8,
  tools_submitted: 3,
  tools_approved: 2,
  requests_made: 5,
  requests_fulfilled: 2,
  reputation_score: 85,
  member_since: '2023-01-15T10:00:00Z'
};

export const mockRecentActivity: ProfileActivity[] = [
  {
    id: 'activity-1',
    type: 'BOOKMARK',
    description: 'Bookmarked ChatGPT',
    entity_id: 'entity-1',
    entity_name: 'ChatGPT',
    entity_slug: 'chatgpt',
    created_at: '2024-01-14T15:30:00Z'
  },
  {
    id: 'activity-2',
    type: 'REVIEW',
    description: 'Reviewed Midjourney',
    entity_id: 'entity-2',
    entity_name: 'Midjourney',
    entity_slug: 'midjourney',
    created_at: '2024-01-13T09:15:00Z'
  },
  {
    id: 'activity-3',
    type: 'SUBMISSION',
    description: 'Submitted new tool: AI Code Assistant',
    entity_id: 'entity-3',
    entity_name: 'AI Code Assistant',
    entity_slug: 'ai-code-assistant',
    created_at: '2024-01-12T14:20:00Z'
  },
  {
    id: 'activity-4',
    type: 'REQUEST',
    description: 'Requested tool: Advanced Image Editor',
    created_at: '2024-01-11T11:45:00Z'
  },
  {
    id: 'activity-5',
    type: 'VOTE',
    description: 'Voted for tool request: Video Generator',
    created_at: '2024-01-10T16:30:00Z'
  }
];

export const mockToolRequests: ToolRequest[] = [
  {
    id: 'request-1',
    user_id: 'user-123',
    tool_name: 'Advanced Image Editor',
    description: 'A powerful AI-powered image editing tool with advanced features like background removal, style transfer, and intelligent cropping.',
    reason: 'This tool would be valuable for content creators and designers who need quick, AI-powered image editing capabilities.',
    category_suggestion: 'Image Editing',
    website_url: 'https://advancedimageeditor.com',
    priority: 'HIGH',
    status: 'UNDER_REVIEW',
    admin_notes: 'Tool looks promising, currently evaluating licensing and integration options.',
    votes: 12,
    created_at: '2024-01-11T11:45:00Z',
    updated_at: '2024-01-13T09:30:00Z'
  },
  {
    id: 'request-2',
    user_id: 'user-123',
    tool_name: 'Video Generator AI',
    description: 'An AI tool that can generate short videos from text descriptions, perfect for social media content.',
    reason: 'Video content is becoming increasingly important, and this tool would help users create engaging videos quickly.',
    category_suggestion: 'Video Generation',
    priority: 'MEDIUM',
    status: 'APPROVED',
    votes: 8,
    created_at: '2024-01-05T14:20:00Z',
    updated_at: '2024-01-08T16:15:00Z'
  },
  {
    id: 'request-3',
    user_id: 'user-123',
    tool_name: 'Code Documentation AI',
    description: 'Automatically generates comprehensive documentation for code repositories.',
    reason: 'Documentation is often neglected in projects, this tool would help maintain better code documentation.',
    category_suggestion: 'Development Tools',
    priority: 'LOW',
    status: 'PENDING',
    votes: 3,
    created_at: '2023-12-28T10:30:00Z',
    updated_at: '2023-12-28T10:30:00Z'
  }
];

export const mockSubmittedTools: UserSubmittedTool[] = [
  {
    id: 'submission-1',
    entity: {
      id: 'entity-3',
      name: 'AI Code Assistant',
      slug: 'ai-code-assistant',
      description: 'An intelligent code completion and suggestion tool that helps developers write better code faster.',
      websiteUrl: 'https://aicodeassistant.dev',
      logoUrl: null,
      entityType: {
        id: 'type-1',
        name: 'AI Tool',
        slug: 'ai-tool',
        description: 'AI-powered tools and applications',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      },
      categories: [
        {
          id: 'cat-1',
          name: 'Development',
          slug: 'development',
          description: 'Development tools and resources',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z'
        },
        {
          id: 'cat-2',
          name: 'Productivity',
          slug: 'productivity',
          description: 'Productivity and efficiency tools',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z'
        }
      ],
      tags: [],
      features: [],
      avgRating: 4.5,
      reviewCount: 12,
      saveCount: 45,
      status: 'published',
      createdAt: '2024-01-12T14:20:00Z',
      updatedAt: '2024-01-15T09:30:00Z'
    },
    submission_status: 'PUBLISHED',
    submitted_at: '2024-01-12T14:20:00Z',
    reviewed_at: '2024-01-15T09:30:00Z',
    reviewer_notes: 'Great tool! Well documented and useful for developers.',
    changes_requested: undefined
  },
  {
    id: 'submission-2',
    entity: {
      id: 'entity-4',
      name: 'Smart Email Writer',
      slug: 'smart-email-writer',
      description: 'AI-powered email composition tool that helps write professional emails quickly.',
      websiteUrl: 'https://smartemailwriter.com',
      logoUrl: null,
      entityType: {
        id: 'type-1',
        name: 'AI Tool',
        slug: 'ai-tool',
        description: 'AI-powered tools and applications',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      },
      categories: [
        {
          id: 'cat-3',
          name: 'Writing',
          slug: 'writing',
          description: 'Writing and content creation tools',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z'
        },
        {
          id: 'cat-2',
          name: 'Productivity',
          slug: 'productivity',
          description: 'Productivity and efficiency tools',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z'
        }
      ],
      tags: [],
      features: [],
      avgRating: 3.8,
      reviewCount: 5,
      saveCount: 23,
      status: 'draft',
      createdAt: '2024-01-08T11:15:00Z',
      updatedAt: '2024-01-10T14:45:00Z'
    },
    submission_status: 'UNDER_REVIEW',
    submitted_at: '2024-01-08T11:15:00Z',
    reviewed_at: undefined,
    reviewer_notes: undefined,
    changes_requested: undefined
  },
  {
    id: 'submission-3',
    entity: {
      id: 'entity-5',
      name: 'Data Analyzer Pro',
      slug: 'data-analyzer-pro',
      description: 'Advanced data analysis tool with AI-powered insights and visualization capabilities.',
      websiteUrl: 'https://dataanalyzerpro.com',
      logoUrl: null,
      entityType: {
        id: 'type-1',
        name: 'AI Tool',
        slug: 'ai-tool',
        description: 'AI-powered tools and applications',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      },
      categories: [
        {
          id: 'cat-4',
          name: 'Data Analysis',
          slug: 'data-analysis',
          description: 'Data analysis and visualization tools',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z'
        },
        {
          id: 'cat-5',
          name: 'Business',
          slug: 'business',
          description: 'Business and enterprise tools',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z'
        }
      ],
      tags: [],
      features: [],
      avgRating: 2.1,
      reviewCount: 3,
      saveCount: 8,
      status: 'draft',
      createdAt: '2023-12-20T16:30:00Z',
      updatedAt: '2023-12-22T10:15:00Z'
    },
    submission_status: 'REJECTED',
    submitted_at: '2023-12-20T16:30:00Z',
    reviewed_at: '2023-12-22T10:15:00Z',
    reviewer_notes: 'Tool needs more detailed documentation and clearer pricing information.',
    changes_requested: 'Please provide more comprehensive documentation, clearer pricing tiers, and additional screenshots of the tool in action.'
  }
];

export const mockCompleteProfileData: CompleteProfileData = {
  user: mockUserProfile,
  preferences: mockUserPreferences,
  stats: mockProfileStats,
  recent_activity: mockRecentActivity
};

// Helper function to simulate API delay
export const simulateApiDelay = (ms: number = 1000) => 
  new Promise(resolve => setTimeout(resolve, ms));
