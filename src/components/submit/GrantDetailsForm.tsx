'use client';

import React from 'react';
import { UseFormRegister, FieldErrors, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CreateEntityDto } from '@/types/entity';

interface GrantDetailsFormProps {
  register: UseFormRegister<CreateEntityDto>;
  errors: FieldErrors<CreateEntityDto>;
  setValue: UseFormSetValue<CreateEntityDto>;
  watch: UseFormWatch<CreateEntityDto>;
}

export default function GrantDetailsForm({ register, errors, setValue, watch }: GrantDetailsFormProps) {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Grant Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Funder Name */}
        <div>
          <Label htmlFor="funder_name">Funder Name</Label>
          <Input
            id="funder_name"
            {...register('details.funder_name')}
            placeholder="NSF, NIH, Google, OpenAI..."
            className="mt-1"
          />
        </div>

        {/* Grant Type */}
        <div>
          <Label htmlFor="grant_type">Grant Type</Label>
          <Select onValueChange={(value) => setValue('details.grant_type', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select grant type..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Research">Research</SelectItem>
              <SelectItem value="Innovation">Innovation</SelectItem>
              <SelectItem value="Education">Education</SelectItem>
              <SelectItem value="Startup">Startup</SelectItem>
              <SelectItem value="Fellowship">Fellowship</SelectItem>
              <SelectItem value="Travel">Travel</SelectItem>
              <SelectItem value="Equipment">Equipment</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Amount */}
        <div>
          <Label htmlFor="amount">Amount</Label>
          <Input
            id="amount"
            {...register('details.amount')}
            placeholder="$50,000, $100K-500K, Up to $1M..."
            className="mt-1"
          />
        </div>

        {/* Deadline */}
        <div>
          <Label htmlFor="deadline">Deadline</Label>
          <Input
            id="deadline"
            type="date"
            {...register('details.deadline')}
            className="mt-1"
          />
        </div>

        {/* Location */}
        <div>
          <Label htmlFor="location">Location</Label>
          <Input
            id="location"
            {...register('details.location')}
            placeholder="US only, Global, EU, Specific regions..."
            className="mt-1"
          />
        </div>

        {/* Application URL */}
        <div>
          <Label htmlFor="application_url">Application URL</Label>
          <Input
            id="application_url"
            type="url"
            {...register('details.application_url')}
            placeholder="https://grants.gov/..."
            className="mt-1"
          />
        </div>
      </div>

      {/* Eligibility */}
      <div>
        <Label htmlFor="eligibility">Eligibility Requirements</Label>
        <Textarea
          id="eligibility"
          {...register('details.eligibility')}
          placeholder="PhD students, Early career researchers, Non-profit organizations..."
          className="mt-1 min-h-[80px]"
        />
      </div>

      {/* Focus Areas */}
      <div>
        <Label htmlFor="focus_areas">Focus Areas (comma-separated)</Label>
        <Textarea
          id="focus_areas"
          {...register('details.focus_areas_text')}
          placeholder="AI Safety, Machine Learning, Computer Vision, NLP, Robotics..."
          className="mt-1 min-h-[80px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter focus areas separated by commas. They will be converted to a list.
        </p>
      </div>
    </div>
  );
}
