'use client';

import React from 'react';
import { UseFormRegister, FieldErrors, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { CreateEntityDto } from '@/types/entity';

interface CourseDetailsFormProps {
  register: UseFormRegister<CreateEntityDto>;
  errors: FieldErrors<CreateEntityDto>;
  setValue: UseFormSetValue<CreateEntityDto>;
  watch: UseFormWatch<CreateEntityDto>;
}

export default function CourseDetailsForm({ register, errors, setValue, watch }: CourseDetailsFormProps) {
  const certificateAvailable = watch('details.certificate_available');

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Course Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Instructor Name */}
        <div>
          <Label htmlFor="instructor_name">Instructor Name</Label>
          <Input
            id="instructor_name"
            {...register('details.instructor_name')}
            placeholder="John Doe"
            className="mt-1"
          />
        </div>

        {/* Duration */}
        <div>
          <Label htmlFor="duration_text">Duration</Label>
          <Input
            id="duration_text"
            {...register('details.duration_text')}
            placeholder="8 weeks, 40 hours, etc."
            className="mt-1"
          />
        </div>

        {/* Skill Level */}
        <div>
          <Label htmlFor="skill_level">Skill Level</Label>
          <Select onValueChange={(value) => setValue('details.skill_level', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select skill level..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="BEGINNER">Beginner</SelectItem>
              <SelectItem value="INTERMEDIATE">Intermediate</SelectItem>
              <SelectItem value="ADVANCED">Advanced</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Language */}
        <div>
          <Label htmlFor="language">Language</Label>
          <Input
            id="language"
            {...register('details.language')}
            placeholder="English, Spanish, etc."
            className="mt-1"
          />
        </div>

        {/* Syllabus URL */}
        <div>
          <Label htmlFor="syllabus_url">Syllabus URL</Label>
          <Input
            id="syllabus_url"
            type="url"
            {...register('details.syllabus_url')}
            placeholder="https://course.com/syllabus"
            className="mt-1"
          />
        </div>

        {/* Enrollment Count */}
        <div>
          <Label htmlFor="enrollment_count">Enrollment Count</Label>
          <Input
            id="enrollment_count"
            type="number"
            {...register('details.enrollment_count', { valueAsNumber: true })}
            placeholder="1000"
            className="mt-1"
          />
        </div>
      </div>

      {/* Certificate Available */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="certificate_available"
          checked={certificateAvailable as boolean}
          onCheckedChange={(checked) => setValue('details.certificate_available', checked)}
        />
        <Label htmlFor="certificate_available">Certificate Available</Label>
      </div>

      {/* Prerequisites */}
      <div>
        <Label htmlFor="prerequisites">Prerequisites (comma-separated)</Label>
        <Textarea
          id="prerequisites"
          {...register('details.prerequisites_text')}
          placeholder="Basic programming knowledge, HTML/CSS, JavaScript fundamentals..."
          className="mt-1 min-h-[80px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter prerequisites separated by commas. They will be converted to a list.
        </p>
      </div>

      {/* Learning Outcomes */}
      <div>
        <Label htmlFor="learning_outcomes">Learning Outcomes (comma-separated)</Label>
        <Textarea
          id="learning_outcomes"
          {...register('details.learning_outcomes_text')}
          placeholder="Build web applications, Understand React concepts, Deploy to production..."
          className="mt-1 min-h-[100px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter learning outcomes separated by commas. They will be converted to a list.
        </p>
      </div>
    </div>
  );
}
