'use client';

import React from 'react';
import { UseFormRegister, FieldErrors, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { CreateEntityDto } from '@/types/entity';

interface DatasetDetailsFormProps {
  register: UseFormRegister<CreateEntityDto>;
  errors: FieldErrors<CreateEntityDto>;
  setValue: UseFormSetValue<CreateEntityDto>;
  watch: UseFormWatch<CreateEntityDto>;
}

export default function DatasetDetailsForm({ register, errors, setValue, watch }: DatasetDetailsFormProps) {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Dataset Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* License */}
        <div>
          <Label htmlFor="license">License</Label>
          <Input
            id="license"
            {...register('details.license')}
            placeholder="MIT, CC BY 4.0, Apache 2.0, Custom..."
            className="mt-1"
          />
        </div>

        {/* Format */}
        <div>
          <Label htmlFor="format">Format</Label>
          <Input
            id="format"
            {...register('details.format')}
            placeholder="CSV, JSON, Parquet, HDF5, Images..."
            className="mt-1"
          />
        </div>

        {/* Size in Bytes */}
        <div>
          <Label htmlFor="size_in_bytes">Size (bytes)</Label>
          <Input
            id="size_in_bytes"
            type="number"
            {...register('details.size_in_bytes', { valueAsNumber: true })}
            placeholder="**********"
            className="mt-1"
          />
          <p className="text-xs text-gray-500 mt-1">
            Size in bytes (e.g., 1GB = ********** bytes)
          </p>
        </div>

        {/* Source URL */}
        <div>
          <Label htmlFor="source_url">Source URL</Label>
          <Input
            id="source_url"
            type="url"
            {...register('details.source_url')}
            placeholder="https://example.com/dataset"
            className="mt-1"
          />
        </div>

        {/* Collection Method */}
        <div>
          <Label htmlFor="collection_method">Collection Method</Label>
          <Input
            id="collection_method"
            {...register('details.collection_method')}
            placeholder="Web scraping, Surveys, Sensors, Manual annotation..."
            className="mt-1"
          />
        </div>

        {/* Update Frequency */}
        <div>
          <Label htmlFor="update_frequency">Update Frequency</Label>
          <Input
            id="update_frequency"
            {...register('details.update_frequency')}
            placeholder="Daily, Weekly, Monthly, Static..."
            className="mt-1"
          />
        </div>
      </div>

      {/* Description */}
      <div>
        <Label htmlFor="dataset_description">Dataset Description</Label>
        <Textarea
          id="dataset_description"
          {...register('details.description')}
          placeholder="Detailed description of the dataset, its contents, structure, and intended use..."
          className="mt-1 min-h-[100px]"
        />
      </div>

      {/* Access Notes */}
      <div>
        <Label htmlFor="access_notes">Access Notes</Label>
        <Textarea
          id="access_notes"
          {...register('details.access_notes')}
          placeholder="Information about how to access the dataset, any requirements, registration needed, etc..."
          className="mt-1 min-h-[80px]"
        />
      </div>
    </div>
  );
}
