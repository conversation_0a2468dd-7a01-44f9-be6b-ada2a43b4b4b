'use client';

import React from 'react';
import { UseFormRegister, FieldErrors, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { CreateEntityDto } from '@/types/entity';

interface PodcastDetailsFormProps {
  register: UseFormRegister<CreateEntityDto>;
  errors: FieldErrors<CreateEntityDto>;
  setValue: UseFormSetValue<CreateEntityDto>;
  watch: UseFormWatch<CreateEntityDto>;
}

export default function PodcastDetailsForm({ register, errors, setValue, watch }: PodcastDetailsFormProps) {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Podcast Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Host */}
        <div>
          <Label htmlFor="host">Host</Label>
          <Input
            id="host"
            {...register('details.host')}
            placeholder="<PERSON>, <PERSON> <PERSON>..."
            className="mt-1"
          />
        </div>

        {/* Frequency */}
        <div>
          <Label htmlFor="frequency">Frequency</Label>
          <Input
            id="frequency"
            {...register('details.frequency')}
            placeholder="Weekly, Bi-weekly, Monthly..."
            className="mt-1"
          />
        </div>

        {/* Average Length */}
        <div>
          <Label htmlFor="average_length">Average Length</Label>
          <Input
            id="average_length"
            {...register('details.average_length')}
            placeholder="60 minutes, 90 minutes..."
            className="mt-1"
          />
        </div>

        {/* Apple Podcasts URL */}
        <div>
          <Label htmlFor="apple_podcasts_url">Apple Podcasts URL</Label>
          <Input
            id="apple_podcasts_url"
            type="url"
            {...register('details.apple_podcasts_url')}
            placeholder="https://podcasts.apple.com/..."
            className="mt-1"
          />
        </div>

        {/* Spotify URL */}
        <div>
          <Label htmlFor="spotify_url">Spotify URL</Label>
          <Input
            id="spotify_url"
            type="url"
            {...register('details.spotify_url')}
            placeholder="https://open.spotify.com/show/..."
            className="mt-1"
          />
        </div>

        {/* Google Podcasts URL */}
        <div>
          <Label htmlFor="google_podcasts_url">Google Podcasts URL</Label>
          <Input
            id="google_podcasts_url"
            type="url"
            {...register('details.google_podcasts_url')}
            placeholder="https://podcasts.google.com/..."
            className="mt-1"
          />
        </div>

        {/* YouTube URL */}
        <div className="md:col-span-2">
          <Label htmlFor="youtube_url">YouTube URL</Label>
          <Input
            id="youtube_url"
            type="url"
            {...register('details.youtube_url')}
            placeholder="https://youtube.com/channel/..."
            className="mt-1"
          />
        </div>
      </div>

      {/* Main Topics */}
      <div>
        <Label htmlFor="main_topics">Main Topics (comma-separated)</Label>
        <Textarea
          id="main_topics"
          {...register('details.main_topics_text')}
          placeholder="AI, Machine Learning, Technology, Science, Philosophy..."
          className="mt-1 min-h-[80px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter main topics separated by commas. They will be converted to a list.
        </p>
      </div>
    </div>
  );
}
