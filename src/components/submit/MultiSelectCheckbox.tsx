'use client';

import React from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

interface Option {
  id: string;
  name: string;
  description?: string;
}

interface MultiSelectCheckboxProps {
  label: string;
  options: Option[];
  selectedIds: string[];
  onSelectionChange: (selectedIds: string[]) => void;
  maxHeight?: string;
  columns?: 1 | 2 | 3;
  showDescription?: boolean;
}

export default function MultiSelectCheckbox({
  label,
  options,
  selectedIds,
  onSelectionChange,
  maxHeight = 'max-h-48',
  columns = 2,
  showDescription = false
}: MultiSelectCheckboxProps) {
  const handleCheckboxChange = (optionId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedIds, optionId]);
    } else {
      onSelectionChange(selectedIds.filter(id => id !== optionId));
    }
  };

  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
  };

  return (
    <div className="space-y-3">
      <Label className="text-base font-medium">{label}</Label>
      
      <div className={`border border-gray-200 rounded-lg p-4 ${maxHeight} overflow-y-auto`}>
        {options.length === 0 ? (
          <p className="text-gray-500 text-sm">No options available</p>
        ) : (
          <div className={`grid ${gridCols[columns]} gap-3`}>
            {options.map((option) => (
              <div key={option.id} className="flex items-start space-x-3">
                <Checkbox
                  id={option.id}
                  checked={selectedIds.includes(option.id)}
                  onCheckedChange={(checked) => 
                    handleCheckboxChange(option.id, checked as boolean)
                  }
                  className="mt-0.5"
                />
                <div className="flex-1 min-w-0">
                  <Label
                    htmlFor={option.id}
                    className="text-sm font-medium text-gray-900 cursor-pointer"
                  >
                    {option.name}
                  </Label>
                  {showDescription && option.description && (
                    <p className="text-xs text-gray-500 mt-1">
                      {option.description}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      
      {selectedIds.length > 0 && (
        <div className="text-sm text-gray-600">
          {selectedIds.length} selected
        </div>
      )}
    </div>
  );
}
