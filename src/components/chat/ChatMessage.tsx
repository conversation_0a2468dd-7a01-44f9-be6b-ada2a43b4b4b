'use client';

import React from 'react';
import { ChatMessage as ChatMessageType } from '@/types/chat';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>rk<PERSON>, ThumbsUp, Co<PERSON>, Share } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ChatResourceCard from '@/components/chat/ChatResourceCard';

interface ChatMessageProps {
  message: ChatMessageType;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const isUser = message.sender === 'user';
  const isAI = message.sender === 'ai';

  const handleCopyMessage = () => {
    navigator.clipboard.writeText(message.text);
  };

  const handleShareMessage = () => {
    if (navigator.share) {
      navigator.share({
        title: 'AI Navigator Response',
        text: message.text,
      });
    }
  };

  return (
    <div className={`flex w-full mb-8 ${isUser ? 'justify-end' : 'justify-start'} group`}>
      <div className={`flex max-w-[85%] sm:max-w-[80%] ${isUser ? 'flex-row-reverse' : 'flex-row'} gap-3 sm:gap-4`}>
        {/* Avatar */}
        <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center shadow-lg ${
          isUser
            ? 'bg-gradient-to-br from-indigo-600 to-purple-600 text-white'
            : 'bg-gradient-to-br from-indigo-500 to-purple-500 text-white'
        }`}>
          {isUser ? (
            <User className="w-5 h-5" />
          ) : (
            <Sparkles className="w-5 h-5" />
          )}
        </div>

        {/* Message Content */}
        <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'} flex-1`}>
          {/* Message Bubble */}
          <div className={`px-4 sm:px-5 py-3 sm:py-4 rounded-2xl max-w-full shadow-lg backdrop-blur-sm border transition-all duration-200 hover:shadow-xl ${
            isUser
              ? 'bg-gradient-to-br from-indigo-600 to-purple-600 text-white rounded-br-md border-indigo-200'
              : 'bg-white/90 text-gray-900 rounded-bl-md border-gray-200/50'
          }`}>
            <p className="text-sm sm:text-base leading-relaxed whitespace-pre-wrap break-words">
              {message.text}
            </p>
          </div>

          {/* Message Actions & Timestamp */}
          <div className={`flex items-center gap-2 mt-2 ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
            <div className={`text-xs text-gray-500 ${isUser ? 'text-right' : 'text-left'}`}>
              {message.timestamp.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>

            {/* Action Buttons (only for AI messages) */}
            {isAI && (
              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopyMessage}
                  className="h-6 w-6 p-0 hover:bg-gray-100"
                >
                  <Copy className="w-3 h-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleShareMessage}
                  className="h-6 w-6 p-0 hover:bg-gray-100"
                >
                  <Share className="w-3 h-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 hover:bg-gray-100"
                >
                  <ThumbsUp className="w-3 h-3" />
                </Button>
              </div>
            )}
          </div>

          {/* Recommended Entities (only for AI messages) */}
          {isAI && message.recommendedEntities && message.recommendedEntities.length > 0 && (
            <div className="mt-6 w-full">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-6 h-6 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <Sparkles className="w-3 h-3 text-white" />
                </div>
                <h4 className="text-sm font-semibold text-gray-800">
                  Recommended for you
                </h4>
              </div>
              <div className="space-y-3">
                {message.recommendedEntities.map((entity, index) => (
                  <ChatResourceCard
                    key={entity.id}
                    entity={entity}
                    index={index}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Error State */}
          {message.metadata?.error && (
            <div className="mt-2 px-3 py-2 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">
                Error: {message.metadata.error}
              </p>
            </div>
          )}

          {/* Loading State */}
          {message.metadata?.isLoading && (
            <div className="mt-2 flex items-center gap-2 text-gray-500">
              <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
              <span className="text-sm">Processing...</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
