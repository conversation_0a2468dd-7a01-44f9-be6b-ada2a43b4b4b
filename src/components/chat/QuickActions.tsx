'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { 
  Search, 
  BookOpen, 
  Code, 
  Palette, 
  Bar<PERSON>hart3, 
  MessageSquare,
  ArrowRight,
  <PERSON>rk<PERSON>,
  Filter,
  Star
} from 'lucide-react';

interface QuickAction {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  prompt: string;
  category: string;
  gradient: string;
}

interface QuickActionsProps {
  onActionClick: (prompt: string) => void;
  isLoading?: boolean;
}

const QuickActions: React.FC<QuickActionsProps> = ({ onActionClick, isLoading = false }) => {
  const quickActions: QuickAction[] = [
    {
      id: 'find-tools',
      label: 'Find AI Tools',
      icon: Search,
      prompt: 'Help me find AI tools for my specific needs',
      category: 'Discovery',
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'coding-help',
      label: 'Coding Assistant',
      icon: Code,
      prompt: 'What are the best AI coding assistants and development tools?',
      category: 'Development',
      gradient: 'from-green-500 to-emerald-500'
    },
    {
      id: 'learn-ai',
      label: 'Learn AI',
      icon: BookOpen,
      prompt: 'Show me beginner-friendly AI courses and learning resources',
      category: 'Education',
      gradient: 'from-purple-500 to-violet-500'
    },
    {
      id: 'creative-tools',
      label: 'Creative Tools',
      icon: Palette,
      prompt: 'I need AI tools for creative work like image and video generation',
      category: 'Creative',
      gradient: 'from-pink-500 to-rose-500'
    },
    {
      id: 'data-analysis',
      label: 'Data Analysis',
      icon: BarChart3,
      prompt: 'Help me find AI tools for data analysis and visualization',
      category: 'Analytics',
      gradient: 'from-orange-500 to-amber-500'
    },
    {
      id: 'business-ai',
      label: 'Business AI',
      icon: MessageSquare,
      prompt: 'What are the best AI tools for small businesses and productivity?',
      category: 'Business',
      gradient: 'from-indigo-500 to-purple-500'
    }
  ];

  const followUpActions = [
    {
      id: 'compare',
      label: 'Compare Options',
      icon: Filter,
      prompt: 'Can you compare these tools and help me choose the best one?'
    },
    {
      id: 'pricing',
      label: 'Pricing Info',
      icon: Star,
      prompt: 'What are the pricing options for these tools?'
    },
    {
      id: 'alternatives',
      label: 'Show Alternatives',
      icon: Search,
      prompt: 'Are there any free or cheaper alternatives to these tools?'
    },
    {
      id: 'getting-started',
      label: 'Getting Started',
      icon: BookOpen,
      prompt: 'How do I get started with these tools? Any tutorials or guides?'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Main Quick Actions */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Sparkles className="w-5 h-5 text-indigo-600" />
          <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {quickActions.map((action) => (
            <Button
              key={action.id}
              variant="outline"
              onClick={() => onActionClick(action.prompt)}
              disabled={isLoading}
              className="h-auto p-4 flex flex-col items-center gap-2 text-center border-2 hover:shadow-lg transition-all duration-200 group"
            >
              <div className={`w-10 h-10 bg-gradient-to-br ${action.gradient} rounded-xl flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-200`}>
                <action.icon className="w-5 h-5 text-white" />
              </div>
              <div>
                <div className="font-medium text-sm text-gray-900">{action.label}</div>
                <div className="text-xs text-gray-500">{action.category}</div>
              </div>
            </Button>
          ))}
        </div>
      </div>

      {/* Follow-up Actions */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <ArrowRight className="w-4 h-4 text-gray-600" />
          <h4 className="text-base font-medium text-gray-800">Follow-up Questions</h4>
        </div>
        <div className="flex flex-wrap gap-2">
          {followUpActions.map((action) => (
            <Button
              key={action.id}
              variant="ghost"
              size="sm"
              onClick={() => onActionClick(action.prompt)}
              disabled={isLoading}
              className="h-auto px-3 py-2 text-sm bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg transition-all duration-200"
            >
              <action.icon className="w-4 h-4 mr-2" />
              {action.label}
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default QuickActions;
