'use client';

import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';

interface FilterLoadingStateProps {
  sections?: number;
  itemsPerSection?: number;
}

const FilterLoadingState: React.FC<FilterLoadingStateProps> = ({ 
  sections = 4, 
  itemsPerSection = 3 
}) => {
  return (
    <div className="space-y-4">
      {/* Header skeleton */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Skeleton className="h-5 w-5" />
          <Skeleton className="h-6 w-32" />
        </div>
        <Skeleton className="h-8 w-20" />
      </div>

      {/* Filter sections skeleton */}
      {Array.from({ length: sections }).map((_, sectionIndex) => (
        <div 
          key={sectionIndex}
          className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-gray-900 shadow-sm"
        >
          {/* Section header */}
          <div className="p-4 border-b border-gray-100 dark:border-gray-800">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Skeleton className="h-8 w-8 rounded-lg" />
                <Skeleton className="h-5 w-24" />
                <Skeleton className="h-4 w-6 rounded-full" />
              </div>
              <Skeleton className="h-4 w-4" />
            </div>
          </div>

          {/* Section content */}
          <div className="p-4 bg-gray-50 dark:bg-gray-800/50 space-y-4">
            {Array.from({ length: itemsPerSection }).map((_, itemIndex) => (
              <div key={itemIndex} className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-8 w-full" />
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default FilterLoadingState;
