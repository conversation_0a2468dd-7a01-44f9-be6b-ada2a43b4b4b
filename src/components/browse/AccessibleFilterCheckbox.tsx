'use client';

import React, { useId, forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { Check } from 'lucide-react';

interface AccessibleFilterCheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type' | 'size'> {
  label: string;
  description?: string;
  error?: string;
  variant?: 'default' | 'card' | 'switch';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ReactNode;
}

const AccessibleFilterCheckbox = forwardRef<HTMLInputElement, AccessibleFilterCheckboxProps>(
  ({ 
    label, 
    description, 
    error, 
    variant = 'default',
    size = 'md',
    icon,
    className,
    id: providedId,
    checked,
    ...props 
  }, ref) => {
    const generatedId = useId();
    const id = providedId || generatedId;
    const descriptionId = `${id}-description`;
    const errorId = `${id}-error`;

    const getSizeStyles = () => {
      switch (size) {
        case 'sm':
          return 'h-3 w-3';
        case 'lg':
          return 'h-5 w-5';
        default:
          return 'h-4 w-4';
      }
    };

    const getLabelSizeStyles = () => {
      switch (size) {
        case 'sm':
          return 'text-xs';
        case 'lg':
          return 'text-base';
        default:
          return 'text-sm';
      }
    };

    if (variant === 'card') {
      return (
        <div className="space-y-1">
          <label
            htmlFor={id}
            className={cn(
              'flex items-center p-3 rounded-lg border cursor-pointer transition-all duration-200',
              'hover:bg-gray-50 dark:hover:bg-gray-800/50',
              checked 
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 ring-1 ring-blue-500' 
                : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900',
              error && 'border-red-300 dark:border-red-600',
              className
            )}
          >
            <div className="flex items-center gap-3 flex-1">
              {/* Custom Checkbox */}
              <div className="relative">
                <input
                  ref={ref}
                  type="checkbox"
                  id={id}
                  checked={checked}
                  aria-describedby={cn(
                    description && descriptionId,
                    error && errorId
                  )}
                  aria-invalid={error ? 'true' : 'false'}
                  className="sr-only"
                  {...props}
                />
                <div className={cn(
                  'flex items-center justify-center rounded border-2 transition-all duration-200',
                  getSizeStyles(),
                  checked 
                    ? 'bg-blue-600 border-blue-600 text-white' 
                    : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600'
                )}>
                  {checked && <Check className="h-3 w-3" />}
                </div>
              </div>

              {/* Icon */}
              {icon && (
                <div className={cn(
                  'flex-shrink-0',
                  checked ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'
                )}>
                  {icon}
                </div>
              )}

              {/* Label and Description */}
              <div className="flex-1 min-w-0">
                <div className={cn(
                  'font-medium text-gray-900 dark:text-gray-100',
                  getLabelSizeStyles()
                )}>
                  {label}
                </div>
                {description && (
                  <div 
                    id={descriptionId}
                    className="text-xs text-gray-500 dark:text-gray-400 mt-0.5"
                  >
                    {description}
                  </div>
                )}
              </div>

              {/* Status Indicator */}
              <div className={cn(
                'flex-shrink-0 text-xs font-medium',
                checked ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400 dark:text-gray-500'
              )}>
                {checked ? '✓' : '○'}
              </div>
            </div>
          </label>

          {/* Error Message */}
          {error && (
            <p 
              id={errorId}
              className="text-xs text-red-600 dark:text-red-400 px-3"
              role="alert"
              aria-live="polite"
            >
              {error}
            </p>
          )}
        </div>
      );
    }

    // Default variant
    return (
      <div className="space-y-1">
        <div className="flex items-center gap-3">
          {/* Checkbox */}
          <input
            ref={ref}
            type="checkbox"
            id={id}
            checked={checked}
            aria-describedby={cn(
              description && descriptionId,
              error && errorId
            )}
            aria-invalid={error ? 'true' : 'false'}
            className={cn(
              'rounded border-gray-300 dark:border-gray-600',
              'text-blue-600 bg-white dark:bg-gray-800',
              'focus:ring-blue-500 focus:ring-2 focus:ring-offset-0',
              'transition-colors duration-200',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              getSizeStyles(),
              error && 'border-red-300 dark:border-red-600 focus:ring-red-500',
              className
            )}
            {...props}
          />

          {/* Icon */}
          {icon && (
            <div className="flex-shrink-0 text-gray-500 dark:text-gray-400">
              {icon}
            </div>
          )}

          {/* Label */}
          <label 
            htmlFor={id}
            className={cn(
              'font-medium text-gray-700 dark:text-gray-300 cursor-pointer flex-1',
              getLabelSizeStyles()
            )}
          >
            {label}
          </label>
        </div>

        {/* Description */}
        {description && (
          <p 
            id={descriptionId}
            className="text-xs text-gray-500 dark:text-gray-400 ml-7"
          >
            {description}
          </p>
        )}

        {/* Error Message */}
        {error && (
          <p 
            id={errorId}
            className="text-xs text-red-600 dark:text-red-400 ml-7"
            role="alert"
            aria-live="polite"
          >
            {error}
          </p>
        )}
      </div>
    );
  }
);

AccessibleFilterCheckbox.displayName = 'AccessibleFilterCheckbox';

export default AccessibleFilterCheckbox;
