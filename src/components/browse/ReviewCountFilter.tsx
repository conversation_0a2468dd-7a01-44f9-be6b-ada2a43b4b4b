'use client';

import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { MessageSquare } from 'lucide-react';

interface ReviewCountFilterProps {
  reviewCountMin?: number;
  reviewCountMax?: number;
  onReviewCountChange: (filterName: 'review_count_min' | 'review_count_max', value: number | null) => void;
}

const ReviewCountFilter: React.FC<ReviewCountFilterProps> = ({
  reviewCountMin,
  reviewCountMax,
  onReviewCountChange,
}) => {
  const handleMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === '') {
      onReviewCountChange('review_count_min', null);
    } else {
      const numValue = parseInt(value, 10);
      if (!isNaN(numValue) && numValue >= 0) {
        onReviewCountChange('review_count_min', numValue);
      }
    }
  };

  const handleMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === '') {
      onReviewCountChange('review_count_max', null);
    } else {
      const numValue = parseInt(value, 10);
      if (!isNaN(numValue) && numValue >= 0) {
        onReviewCountChange('review_count_max', numValue);
      }
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <MessageSquare className="h-4 w-4 text-blue-500" />
        <Label className="text-sm font-medium">Review Count Range</Label>
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        <div className="space-y-1">
          <Label htmlFor="review-count-min" className="text-xs text-muted-foreground">
            Min Reviews
          </Label>
          <Input
            id="review-count-min"
            type="number"
            min="0"
            placeholder="0"
            value={reviewCountMin || ''}
            onChange={handleMinChange}
            className="h-8 text-xs"
          />
        </div>
        
        <div className="space-y-1">
          <Label htmlFor="review-count-max" className="text-xs text-muted-foreground">
            Max Reviews
          </Label>
          <Input
            id="review-count-max"
            type="number"
            min="0"
            placeholder="1000"
            value={reviewCountMax || ''}
            onChange={handleMaxChange}
            className="h-8 text-xs"
          />
        </div>
      </div>
      
      <div className="text-xs text-muted-foreground">
        Filter by number of reviews
      </div>
    </div>
  );
};

export default ReviewCountFilter;
