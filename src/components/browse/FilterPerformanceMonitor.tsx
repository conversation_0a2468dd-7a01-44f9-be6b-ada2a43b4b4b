'use client';

import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  Activity, 
  Clock, 
  Database, 
  ChevronDown, 
  Zap,
  TrendingUp,
  AlertTriangle 
} from 'lucide-react';

interface PerformanceMetrics {
  apiCallCount: number;
  averageResponseTime: number;
  cacheHitRate: number;
  lastUpdateTime: number;
  slowQueries: number;
  errorCount: number;
}

interface FilterPerformanceMonitorProps {
  metrics: PerformanceMetrics;
  isVisible?: boolean;
  onToggleVisibility?: () => void;
  className?: string;
}

const FilterPerformanceMonitor: React.FC<FilterPerformanceMonitorProps> = ({
  metrics,
  isVisible = false,
  onToggleVisibility,
  className = '',
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getPerformanceStatus = () => {
    if (metrics.errorCount > 0) return 'error';
    if (metrics.averageResponseTime > 2000) return 'warning';
    if (metrics.cacheHitRate < 50) return 'warning';
    return 'good';
  };

  const getStatusColor = () => {
    const status = getPerformanceStatus();
    switch (status) {
      case 'error':
        return 'text-red-600 dark:text-red-400';
      case 'warning':
        return 'text-yellow-600 dark:text-yellow-400';
      default:
        return 'text-green-600 dark:text-green-400';
    }
  };

  const getStatusIcon = () => {
    const status = getPerformanceStatus();
    switch (status) {
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <Zap className="h-4 w-4 text-green-500" />;
    }
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (!isVisible) {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={onToggleVisibility}
        className={`fixed bottom-4 right-4 z-50 bg-white dark:bg-gray-800 shadow-lg border ${className}`}
        title="Show performance metrics"
      >
        <Activity className="h-4 w-4" />
      </Button>
    );
  }

  return (
    <div className={`fixed bottom-4 right-4 z-50 bg-white dark:bg-gray-800 shadow-lg border rounded-lg p-3 min-w-[280px] ${className}`}>
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <span className="text-sm font-medium">Filter Performance</span>
            <Badge 
              variant="outline" 
              className={`text-xs ${getStatusColor()}`}
            >
              {getPerformanceStatus().toUpperCase()}
            </Badge>
          </div>
          <div className="flex items-center gap-1">
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                <ChevronDown className={`h-3 w-3 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
              </Button>
            </CollapsibleTrigger>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleVisibility}
              className="h-6 w-6 p-0"
              title="Hide performance metrics"
            >
              ×
            </Button>
          </div>
        </div>

        {/* Quick metrics */}
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="flex items-center gap-1">
            <Database className="h-3 w-3 text-blue-500" />
            <span>{metrics.apiCallCount} calls</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-purple-500" />
            <span>{formatTime(metrics.averageResponseTime)}</span>
          </div>
        </div>

        <CollapsibleContent className="mt-3">
          <div className="space-y-3 text-xs">
            {/* Detailed metrics */}
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Cache Hit Rate:</span>
                  <span className={metrics.cacheHitRate > 70 ? 'text-green-600' : 'text-yellow-600'}>
                    {formatPercentage(metrics.cacheHitRate)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Slow Queries:</span>
                  <span className={metrics.slowQueries > 0 ? 'text-yellow-600' : 'text-green-600'}>
                    {metrics.slowQueries}
                  </span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Errors:</span>
                  <span className={metrics.errorCount > 0 ? 'text-red-600' : 'text-green-600'}>
                    {metrics.errorCount}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Last Update:</span>
                  <span className="text-gray-600 dark:text-gray-400">
                    {formatTime(Date.now() - metrics.lastUpdateTime)} ago
                  </span>
                </div>
              </div>
            </div>

            {/* Performance tips */}
            {getPerformanceStatus() !== 'good' && (
              <div className="mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800">
                <div className="flex items-start gap-2">
                  <TrendingUp className="h-3 w-3 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div className="text-xs text-yellow-800 dark:text-yellow-200">
                    {metrics.averageResponseTime > 2000 && (
                      <p>• Slow response times detected. Consider reducing filter complexity.</p>
                    )}
                    {metrics.cacheHitRate < 50 && (
                      <p>• Low cache hit rate. Try using similar filter combinations.</p>
                    )}
                    {metrics.errorCount > 0 && (
                      <p>• API errors detected. Check network connection.</p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
};

export default FilterPerformanceMonitor;
