'use client';

import React from 'react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Clock, XCircle, Archive, AlertTriangle, Edit } from 'lucide-react';

interface StatusQualityFilterProps {
  status?: 'PENDING' | 'ACTIVE' | 'REJECTED' | 'INACTIVE' | 'ARCHIVED' | 'NEEDS_REVISION';
  onFilterChange: (filterName: 'status', value: string | null) => void;
}

const STATUS_OPTIONS = [
  { 
    value: 'ACTIVE', 
    label: 'Active', 
    icon: CheckCircle, 
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    description: 'Approved and live resources'
  },
  { 
    value: 'PENDING', 
    label: 'Pending Review', 
    icon: Clock, 
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100',
    description: 'Awaiting moderation'
  },
  { 
    value: 'NEEDS_REVISION', 
    label: 'Needs Revision', 
    icon: Edit, 
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
    description: 'Requires updates'
  },
  { 
    value: 'INACTIVE', 
    label: 'Inactive', 
    icon: XCircle, 
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
    description: 'Temporarily disabled'
  },
  { 
    value: 'REJECTED', 
    label: 'Rejected', 
    icon: XCircle, 
    color: 'text-red-600',
    bgColor: 'bg-red-100',
    description: 'Did not meet guidelines'
  },
  { 
    value: 'ARCHIVED', 
    label: 'Archived', 
    icon: Archive, 
    color: 'text-gray-500',
    bgColor: 'bg-gray-50',
    description: 'No longer maintained'
  },
];

const StatusQualityFilter: React.FC<StatusQualityFilterProps> = ({
  status,
  onFilterChange,
}) => {
  const handleStatusChange = (value: string) => {
    if (value === 'all') {
      onFilterChange('status', null);
    } else {
      onFilterChange('status', value);
    }
  };

  const selectedStatus = STATUS_OPTIONS.find(option => option.value === status);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <AlertTriangle className="h-4 w-4 text-amber-500" />
        <Label className="text-sm font-medium">Status & Quality</Label>
      </div>
      
      {/* Status Filter */}
      <div className="space-y-3">
        <Label className="text-xs text-muted-foreground">Resource Status</Label>
        
        <Select value={status || 'all'} onValueChange={handleStatusChange}>
          <SelectTrigger className="h-10">
            <SelectValue placeholder="Filter by status...">
              {selectedStatus ? (
                <div className="flex items-center gap-2">
                  <selectedStatus.icon className={`h-4 w-4 ${selectedStatus.color}`} />
                  <span>{selectedStatus.label}</span>
                </div>
              ) : (
                'All Statuses'
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">
              <div className="flex items-center gap-2">
                <div className="h-4 w-4" /> {/* Spacer */}
                <span>All Statuses</span>
              </div>
            </SelectItem>
            {STATUS_OPTIONS.map(option => {
              const Icon = option.icon;
              return (
                <SelectItem key={option.value} value={option.value}>
                  <div className="flex items-center gap-2">
                    <Icon className={`h-4 w-4 ${option.color}`} />
                    <div className="flex flex-col">
                      <span>{option.label}</span>
                      <span className="text-xs text-muted-foreground">{option.description}</span>
                    </div>
                  </div>
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>

        {/* Status explanation */}
        <div className="text-xs text-muted-foreground bg-gray-50 p-3 rounded-md">
          <p className="font-medium mb-2">Status Guide:</p>
          <div className="grid grid-cols-1 gap-1">
            {STATUS_OPTIONS.slice(0, 3).map(option => {
              const Icon = option.icon;
              return (
                <div key={option.value} className="flex items-center gap-2">
                  <Icon className={`h-3 w-3 ${option.color}`} />
                  <span className="font-medium">{option.label}:</span>
                  <span>{option.description}</span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Current selection display */}
        {status && (
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">Filtering by:</span>
            <Badge 
              variant="secondary" 
              className={`${selectedStatus?.bgColor} ${selectedStatus?.color} border-0`}
            >
              {selectedStatus?.icon && <selectedStatus.icon className="h-3 w-3 mr-1" />}
              {selectedStatus?.label}
            </Badge>
          </div>
        )}
      </div>

      {/* Quality indicators info */}
      <div className="bg-blue-50 p-3 rounded-md">
        <div className="flex items-center gap-2 mb-2">
          <CheckCircle className="h-4 w-4 text-blue-600" />
          <span className="text-sm font-medium text-blue-900">Quality Indicators</span>
        </div>
        <div className="text-xs text-blue-800 space-y-1">
          <p>• <strong>Active</strong> resources have been verified and approved</p>
          <p>• Use rating and review filters for community validation</p>
          <p>• Check affiliate status for transparency</p>
        </div>
      </div>
    </div>
  );
};

export default StatusQualityFilter;
