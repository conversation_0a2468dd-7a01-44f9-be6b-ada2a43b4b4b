'use client';

import React, { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { X, Plus, Monitor, Smartphone, Globe, Code, Zap } from 'lucide-react';

interface PlatformIntegrationFilterProps {
  integrations?: string[];
  platforms?: string[];
  targetAudience?: string[];
  onFilterChange: (filterName: 'integrations' | 'platforms' | 'targetAudience', value: string[] | null) => void;
}

// Common platforms
const COMMON_PLATFORMS = [
  { value: 'Web', label: 'Web Browser', icon: Globe },
  { value: 'Windows', label: 'Windows', icon: Monitor },
  { value: 'macOS', label: 'macOS', icon: Monitor },
  { value: 'Linux', label: 'Linux', icon: Monitor },
  { value: 'iOS', label: 'iOS', icon: Smartphone },
  { value: 'Android', label: 'Android', icon: Smartphone },
  { value: 'Chrome Extension', label: 'Chrome Extension', icon: Globe },
  { value: 'API', label: 'API/SDK', icon: Code },
];

// Common integrations
const COMMON_INTEGRATIONS = [
  'GitHub', 'Slack', 'Discord', 'Zapier', 'Google Workspace', 'Microsoft 365',
  'Notion', 'Trello', 'Asana', 'Jira', 'Figma', 'Adobe Creative Suite',
  'Salesforce', 'HubSpot', 'Shopify', 'WordPress', 'Stripe', 'PayPal'
];

// Target audiences
const TARGET_AUDIENCES = [
  'Developers', 'Designers', 'Marketers', 'Content Creators', 'Data Scientists',
  'Product Managers', 'Students', 'Researchers', 'Entrepreneurs', 'Small Business',
  'Enterprise', 'Freelancers', 'Agencies', 'Non-profits', 'Educators'
];

const PlatformIntegrationFilter: React.FC<PlatformIntegrationFilterProps> = ({
  integrations = [],
  platforms = [],
  targetAudience = [],
  onFilterChange,
}) => {
  const [customIntegration, setCustomIntegration] = useState('');
  const [customPlatform, setCustomPlatform] = useState('');
  const [customAudience, setCustomAudience] = useState('');

  const handleToggle = (filterName: 'integrations' | 'platforms' | 'targetAudience', value: string, currentValues: string[]) => {
    const newValues = currentValues.includes(value)
      ? currentValues.filter(v => v !== value)
      : [...currentValues, value];
    
    onFilterChange(filterName, newValues.length > 0 ? newValues : null);
  };

  const handleAddCustom = (filterName: 'integrations' | 'platforms' | 'targetAudience', customValue: string, currentValues: string[]) => {
    if (customValue.trim() && !currentValues.includes(customValue.trim())) {
      const newValues = [...currentValues, customValue.trim()];
      onFilterChange(filterName, newValues);
      
      // Clear the input
      if (filterName === 'integrations') setCustomIntegration('');
      if (filterName === 'platforms') setCustomPlatform('');
      if (filterName === 'targetAudience') setCustomAudience('');
    }
  };

  const handleRemove = (filterName: 'integrations' | 'platforms' | 'targetAudience', value: string, currentValues: string[]) => {
    const newValues = currentValues.filter(v => v !== value);
    onFilterChange(filterName, newValues.length > 0 ? newValues : null);
  };

  return (
    <div className="space-y-6">
      {/* Platforms */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Monitor className="h-4 w-4 text-blue-500" />
          <Label className="text-sm font-medium text-gray-900 dark:text-gray-100">Platforms & OS</Label>
        </div>
        
        <div className="grid grid-cols-2 gap-2">
          {COMMON_PLATFORMS.map(platform => {
            const Icon = platform.icon;
            const isSelected = platforms.includes(platform.value);
            return (
              <Button
                key={platform.value}
                variant={isSelected ? "default" : "outline"}
                size="sm"
                onClick={() => handleToggle('platforms', platform.value, platforms)}
                className={`justify-start h-9 text-xs font-medium transition-all duration-200 ${
                  isSelected
                    ? 'bg-blue-600 hover:bg-blue-700 border-blue-600 text-white shadow-md'
                    : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-blue-900/20 dark:hover:border-blue-500'
                }`}
              >
                <Icon className="h-3 w-3 mr-2 flex-shrink-0" />
                <span className="truncate">{platform.label}</span>
              </Button>
            );
          })}
        </div>

        {/* Custom platform input */}
        <div className="flex gap-2">
          <Input
            placeholder="Add custom platform..."
            value={customPlatform}
            onChange={(e) => setCustomPlatform(e.target.value)}
            className="h-8 text-xs"
            onKeyPress={(e) => e.key === 'Enter' && handleAddCustom('platforms', customPlatform, platforms)}
          />
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleAddCustom('platforms', customPlatform, platforms)}
            className="h-8 px-3"
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>

        {/* Selected platforms */}
        {platforms.length > 0 && (
          <div className="flex flex-wrap gap-2 pt-1">
            {platforms.map(platform => (
              <Badge
                key={platform}
                variant="secondary"
                className="text-xs cursor-pointer hover:bg-red-100 dark:hover:bg-red-900/30 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 px-2 py-1"
                onClick={() => handleRemove('platforms', platform, platforms)}
              >
                {platform}
                <X className="ml-1 h-3 w-3" />
              </Badge>
            ))}
          </div>
        )}
      </div>

      {/* Integrations */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Zap className="h-4 w-4 text-green-500" />
          <Label className="text-sm font-medium text-gray-900 dark:text-gray-100">Integrations</Label>
        </div>
        
        <div className="grid grid-cols-3 gap-2">
          {COMMON_INTEGRATIONS.map(integration => {
            const isSelected = integrations.includes(integration);
            return (
              <Button
                key={integration}
                variant={isSelected ? "default" : "outline"}
                size="sm"
                onClick={() => handleToggle('integrations', integration, integrations)}
                className={`h-8 text-xs font-medium transition-all duration-200 ${
                  isSelected
                    ? 'bg-blue-600 hover:bg-blue-700 border-blue-600 text-white shadow-md'
                    : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-blue-900/20 dark:hover:border-blue-500'
                }`}
              >
                <span className="truncate">{integration}</span>
              </Button>
            );
          })}
        </div>

        {/* Custom integration input */}
        <div className="flex gap-2">
          <Input
            placeholder="Add custom integration..."
            value={customIntegration}
            onChange={(e) => setCustomIntegration(e.target.value)}
            className="h-8 text-xs"
            onKeyPress={(e) => e.key === 'Enter' && handleAddCustom('integrations', customIntegration, integrations)}
          />
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleAddCustom('integrations', customIntegration, integrations)}
            className="h-8 px-3"
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>

        {/* Selected integrations */}
        {integrations.length > 0 && (
          <div className="flex flex-wrap gap-2 pt-1">
            {integrations.map(integration => (
              <Badge
                key={integration}
                variant="secondary"
                className="text-xs cursor-pointer hover:bg-red-100 dark:hover:bg-red-900/30 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 px-2 py-1"
                onClick={() => handleRemove('integrations', integration, integrations)}
              >
                {integration}
                <X className="ml-1 h-3 w-3" />
              </Badge>
            ))}
          </div>
        )}
      </div>

      {/* Target Audience */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Monitor className="h-4 w-4 text-purple-500" />
          <Label className="text-sm font-medium text-gray-900 dark:text-gray-100">Target Audience</Label>
        </div>
        
        <div className="grid grid-cols-3 gap-2">
          {TARGET_AUDIENCES.map(audience => {
            const isSelected = targetAudience.includes(audience);
            return (
              <Button
                key={audience}
                variant={isSelected ? "default" : "outline"}
                size="sm"
                onClick={() => handleToggle('targetAudience', audience, targetAudience)}
                className={`h-8 text-xs font-medium transition-all duration-200 ${
                  isSelected
                    ? 'bg-blue-600 hover:bg-blue-700 border-blue-600 text-white shadow-md'
                    : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-blue-900/20 dark:hover:border-blue-500'
                }`}
              >
                <span className="truncate">{audience}</span>
              </Button>
            );
          })}
        </div>

        {/* Custom audience input */}
        <div className="flex gap-2">
          <Input
            placeholder="Add custom audience..."
            value={customAudience}
            onChange={(e) => setCustomAudience(e.target.value)}
            className="h-8 text-xs"
            onKeyPress={(e) => e.key === 'Enter' && handleAddCustom('targetAudience', customAudience, targetAudience)}
          />
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleAddCustom('targetAudience', customAudience, targetAudience)}
            className="h-8 px-3"
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>

        {/* Selected audiences */}
        {targetAudience.length > 0 && (
          <div className="flex flex-wrap gap-2 pt-1">
            {targetAudience.map(audience => (
              <Badge
                key={audience}
                variant="secondary"
                className="text-xs cursor-pointer hover:bg-red-100 dark:hover:bg-red-900/30 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 px-2 py-1"
                onClick={() => handleRemove('targetAudience', audience, targetAudience)}
              >
                {audience}
                <X className="ml-1 h-3 w-3" />
              </Badge>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default PlatformIntegrationFilter;
