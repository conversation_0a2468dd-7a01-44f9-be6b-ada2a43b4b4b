'use client';

import React, { useId, forwardRef } from 'react';
import { cn } from '@/lib/utils';

interface AccessibleFilterInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  description?: string;
  error?: string;
  required?: boolean;
  icon?: React.ReactNode;
  suffix?: React.ReactNode;
  variant?: 'default' | 'search' | 'number' | 'date';
}

const AccessibleFilterInput = forwardRef<HTMLInputElement, AccessibleFilterInputProps>(
  ({ 
    label, 
    description, 
    error, 
    required = false, 
    icon, 
    suffix, 
    variant = 'default',
    className,
    id: providedId,
    ...props 
  }, ref) => {
    const generatedId = useId();
    const id = providedId || generatedId;
    const descriptionId = `${id}-description`;
    const errorId = `${id}-error`;

    const getVariantStyles = () => {
      switch (variant) {
        case 'search':
          return 'pl-10 pr-4 py-2.5 text-sm';
        case 'number':
          return 'px-3 py-2 text-sm text-right';
        case 'date':
          return 'px-3 py-2 text-sm';
        default:
          return 'px-3 py-2 text-sm';
      }
    };

    const baseInputStyles = cn(
      'w-full border rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100',
      'placeholder-gray-500 dark:placeholder-gray-400',
      'focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
      'transition-colors duration-200',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      error 
        ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500' 
        : 'border-gray-300 dark:border-gray-600',
      getVariantStyles(),
      className
    );

    return (
      <div className="space-y-2">
        {/* Label */}
        <label 
          htmlFor={id}
          className={cn(
            'block text-sm font-medium text-gray-700 dark:text-gray-300',
            required && "after:content-['*'] after:ml-0.5 after:text-red-500"
          )}
        >
          {label}
        </label>

        {/* Input Container */}
        <div className="relative">
          {/* Icon */}
          {icon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <div className="text-gray-400 dark:text-gray-500">
                {icon}
              </div>
            </div>
          )}

          {/* Input */}
          <input
            ref={ref}
            id={id}
            aria-describedby={cn(
              description && descriptionId,
              error && errorId
            )}
            aria-invalid={error ? 'true' : 'false'}
            aria-required={required}
            className={baseInputStyles}
            {...props}
          />

          {/* Suffix */}
          {suffix && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              {suffix}
            </div>
          )}
        </div>

        {/* Description */}
        {description && (
          <p 
            id={descriptionId}
            className="text-xs text-gray-500 dark:text-gray-400"
          >
            {description}
          </p>
        )}

        {/* Error Message */}
        {error && (
          <p 
            id={errorId}
            className="text-xs text-red-600 dark:text-red-400"
            role="alert"
            aria-live="polite"
          >
            {error}
          </p>
        )}
      </div>
    );
  }
);

AccessibleFilterInput.displayName = 'AccessibleFilterInput';

export default AccessibleFilterInput;
