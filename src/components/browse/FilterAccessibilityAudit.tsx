'use client';

import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  Shield, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  ChevronDown,
  Eye,
  Keyboard,
  MousePointer,
  Volume2
} from 'lucide-react';

interface AccessibilityIssue {
  id: string;
  severity: 'error' | 'warning' | 'info';
  category: 'keyboard' | 'screen-reader' | 'visual' | 'interaction';
  element: string;
  description: string;
  recommendation: string;
  wcagGuideline?: string;
}

interface AccessibilityAuditResults {
  score: number;
  totalChecks: number;
  passedChecks: number;
  issues: AccessibilityIssue[];
  lastAuditTime: number;
}

interface FilterAccessibilityAuditProps {
  isVisible?: boolean;
  onToggleVisibility?: () => void;
  className?: string;
}

const FilterAccessibilityAudit: React.FC<FilterAccessibilityAuditProps> = ({
  isVisible = false,
  onToggleVisibility,
  className = '',
}) => {
  const [auditResults, setAuditResults] = useState<AccessibilityAuditResults | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});

  // Mock audit function - in real implementation, this would use axe-core or similar
  const runAccessibilityAudit = async () => {
    setIsRunning(true);
    
    // Simulate audit delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock audit results
    const mockResults: AccessibilityAuditResults = {
      score: 87,
      totalChecks: 25,
      passedChecks: 22,
      lastAuditTime: Date.now(),
      issues: [
        {
          id: '1',
          severity: 'warning',
          category: 'keyboard',
          element: 'Filter dropdown',
          description: 'Dropdown not fully keyboard accessible',
          recommendation: 'Add arrow key navigation and proper focus management',
          wcagGuideline: 'WCAG 2.1.1 (Keyboard)'
        },
        {
          id: '2',
          severity: 'error',
          category: 'screen-reader',
          element: 'Active filters',
          description: 'Missing aria-live region for filter updates',
          recommendation: 'Add aria-live="polite" to announce filter changes',
          wcagGuideline: 'WCAG 4.1.3 (Status Messages)'
        },
        {
          id: '3',
          severity: 'info',
          category: 'visual',
          element: 'Filter buttons',
          description: 'Color contrast could be improved',
          recommendation: 'Increase contrast ratio to meet AA standards',
          wcagGuideline: 'WCAG 1.4.3 (Contrast)'
        }
      ]
    };
    
    setAuditResults(mockResults);
    setIsRunning(false);
  };

  // Auto-run audit on mount
  useEffect(() => {
    if (isVisible && !auditResults) {
      runAccessibilityAudit();
    }
  }, [isVisible]);

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 dark:text-green-400';
    if (score >= 70) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'keyboard':
        return <Keyboard className="h-4 w-4" />;
      case 'screen-reader':
        return <Volume2 className="h-4 w-4" />;
      case 'visual':
        return <Eye className="h-4 w-4" />;
      default:
        return <MousePointer className="h-4 w-4" />;
    }
  };

  const groupedIssues = auditResults?.issues.reduce((acc, issue) => {
    if (!acc[issue.category]) {
      acc[issue.category] = [];
    }
    acc[issue.category].push(issue);
    return acc;
  }, {} as Record<string, AccessibilityIssue[]>) || {};

  if (!isVisible) {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={onToggleVisibility}
        className={`fixed bottom-16 right-4 z-50 bg-white dark:bg-gray-800 shadow-lg border ${className}`}
        title="Show accessibility audit"
      >
        <Shield className="h-4 w-4" />
      </Button>
    );
  }

  return (
    <div className={`fixed bottom-16 right-4 z-50 bg-white dark:bg-gray-800 shadow-lg border rounded-lg p-4 min-w-[320px] max-w-[400px] ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          <span className="font-medium text-gray-900 dark:text-gray-100">
            Accessibility Audit
          </span>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={runAccessibilityAudit}
            disabled={isRunning}
            className="h-6 px-2 text-xs"
          >
            {isRunning ? 'Running...' : 'Re-run'}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleVisibility}
            className="h-6 w-6 p-0"
            title="Hide accessibility audit"
          >
            ×
          </Button>
        </div>
      </div>

      {/* Loading State */}
      {isRunning && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Running accessibility audit...
          </p>
        </div>
      )}

      {/* Results */}
      {auditResults && !isRunning && (
        <div className="space-y-4">
          {/* Score */}
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
            <div className={`text-2xl font-bold ${getScoreColor(auditResults.score)}`}>
              {auditResults.score}%
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">
              Accessibility Score
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {auditResults.passedChecks}/{auditResults.totalChecks} checks passed
            </div>
          </div>

          {/* Summary */}
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="text-center">
              <div className="text-red-600 dark:text-red-400 font-medium">
                {auditResults.issues.filter(i => i.severity === 'error').length}
              </div>
              <div className="text-gray-600 dark:text-gray-400">Errors</div>
            </div>
            <div className="text-center">
              <div className="text-yellow-600 dark:text-yellow-400 font-medium">
                {auditResults.issues.filter(i => i.severity === 'warning').length}
              </div>
              <div className="text-gray-600 dark:text-gray-400">Warnings</div>
            </div>
            <div className="text-center">
              <div className="text-blue-600 dark:text-blue-400 font-medium">
                {auditResults.issues.filter(i => i.severity === 'info').length}
              </div>
              <div className="text-gray-600 dark:text-gray-400">Info</div>
            </div>
          </div>

          {/* Issues by Category */}
          {Object.entries(groupedIssues).map(([category, issues]) => (
            <Collapsible
              key={category}
              open={expandedCategories[category]}
              onOpenChange={(open) => 
                setExpandedCategories(prev => ({ ...prev, [category]: open }))
              }
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full justify-between p-2 h-auto text-left"
                >
                  <div className="flex items-center gap-2">
                    {getCategoryIcon(category)}
                    <span className="text-sm font-medium capitalize">
                      {category.replace('-', ' ')}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {issues.length}
                    </Badge>
                  </div>
                  <ChevronDown className={`h-3 w-3 transition-transform ${
                    expandedCategories[category] ? 'rotate-180' : ''
                  }`} />
                </Button>
              </CollapsibleTrigger>
              
              <CollapsibleContent className="space-y-2 px-2">
                {issues.map(issue => (
                  <div
                    key={issue.id}
                    className="p-2 bg-gray-50 dark:bg-gray-800/50 rounded text-xs space-y-1"
                  >
                    <div className="flex items-start gap-2">
                      {getSeverityIcon(issue.severity)}
                      <div className="flex-1">
                        <div className="font-medium text-gray-900 dark:text-gray-100">
                          {issue.element}
                        </div>
                        <div className="text-gray-600 dark:text-gray-400">
                          {issue.description}
                        </div>
                        <div className="text-blue-600 dark:text-blue-400 mt-1">
                          💡 {issue.recommendation}
                        </div>
                        {issue.wcagGuideline && (
                          <div className="text-gray-500 dark:text-gray-400 mt-1">
                            📋 {issue.wcagGuideline}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>
          ))}

          {/* Last Audit Time */}
          <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
            Last audit: {new Date(auditResults.lastAuditTime).toLocaleTimeString()}
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterAccessibilityAudit;
