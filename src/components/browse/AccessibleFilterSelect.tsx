'use client';

import React, { useState, useRef, useEffect, useId } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown, Check, X } from 'lucide-react';

interface Option {
  value: string;
  label: string;
  description?: string;
  icon?: React.ReactNode;
  disabled?: boolean;
}

interface AccessibleFilterSelectProps {
  label: string;
  description?: string;
  error?: string;
  options: Option[];
  value?: string | string[];
  onChange: (value: string | string[]) => void;
  placeholder?: string;
  multiple?: boolean;
  searchable?: boolean;
  required?: boolean;
  disabled?: boolean;
  maxHeight?: string;
  className?: string;
}

const AccessibleFilterSelect: React.FC<AccessibleFilterSelectProps> = ({
  label,
  description,
  error,
  options,
  onChange,
  placeholder = 'Select an option...',
  multiple = false,
  value = multiple ? [] : '',
  searchable = false,
  required = false,
  disabled = false,
  maxHeight = '200px',
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [focusedIndex, setFocusedIndex] = useState(-1);
  
  const selectRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const listboxRef = useRef<HTMLUListElement>(null);
  
  const id = useId();
  const descriptionId = `${id}-description`;
  const errorId = `${id}-error`;
  const listboxId = `${id}-listbox`;

  // Filter options based on search term
  const filteredOptions = searchable && searchTerm
    ? options.filter(option => 
        option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        option.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;

  // Get selected options for display
  const selectedOptions = multiple 
    ? options.filter(option => (value as string[]).includes(option.value))
    : options.find(option => option.value === value);

  // Handle option selection
  const handleOptionSelect = (optionValue: string) => {
    if (multiple) {
      const currentValues = value as string[];
      const newValues = currentValues.includes(optionValue)
        ? currentValues.filter(v => v !== optionValue)
        : [...currentValues, optionValue];
      onChange(newValues);
    } else {
      onChange(optionValue);
      setIsOpen(false);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        } else {
          setFocusedIndex(prev => 
            prev < filteredOptions.length - 1 ? prev + 1 : 0
          );
        }
        break;
      case 'ArrowUp':
        event.preventDefault();
        if (isOpen) {
          setFocusedIndex(prev => 
            prev > 0 ? prev - 1 : filteredOptions.length - 1
          );
        }
        break;
      case 'Enter':
      case ' ':
        event.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        } else if (focusedIndex >= 0) {
          handleOptionSelect(filteredOptions[focusedIndex].value);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setFocusedIndex(-1);
        break;
      case 'Tab':
        setIsOpen(false);
        break;
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, searchable]);

  // Reset focused index when options change
  useEffect(() => {
    setFocusedIndex(-1);
  }, [filteredOptions]);

  const getDisplayValue = () => {
    if (multiple) {
      const selected = selectedOptions as Option[];
      if (selected.length === 0) return placeholder;
      if (selected.length === 1) return selected[0].label;
      return `${selected.length} selected`;
    } else {
      const selected = selectedOptions as Option | undefined;
      return selected ? selected.label : placeholder;
    }
  };

  return (
    <div className={cn('space-y-2', className)}>
      {/* Label */}
      <label 
        htmlFor={id}
        className={cn(
          'block text-sm font-medium text-gray-700 dark:text-gray-300',
          required && "after:content-['*'] after:ml-0.5 after:text-red-500"
        )}
      >
        {label}
      </label>

      {/* Select Container */}
      <div ref={selectRef} className="relative">
        {/* Trigger Button */}
        <button
          id={id}
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          aria-haspopup="listbox"
          aria-expanded={isOpen}
          aria-labelledby={id}
          aria-describedby={cn(
            description && descriptionId,
            error && errorId
          )}
          aria-invalid={error ? 'true' : 'false'}
          aria-required={required}
          className={cn(
            'w-full flex items-center justify-between px-3 py-2 text-sm text-left',
            'border rounded-md bg-white dark:bg-gray-800',
            'text-gray-900 dark:text-gray-100',
            'focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'transition-colors duration-200',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            error 
              ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500' 
              : 'border-gray-300 dark:border-gray-600',
            !selectedOptions && 'text-gray-500 dark:text-gray-400'
          )}
        >
          <span className="truncate">{getDisplayValue()}</span>
          <ChevronDown className={cn(
            'h-4 w-4 text-gray-400 transition-transform duration-200',
            isOpen && 'rotate-180'
          )} />
        </button>

        {/* Dropdown */}
        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg">
            {/* Search Input */}
            {searchable && (
              <div className="p-2 border-b border-gray-200 dark:border-gray-700">
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search options..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            )}

            {/* Options List */}
            <ul
              ref={listboxRef}
              id={listboxId}
              role="listbox"
              aria-multiselectable={multiple}
              style={{ maxHeight }}
              className="overflow-auto py-1"
            >
              {filteredOptions.length === 0 ? (
                <li className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                  No options found
                </li>
              ) : (
                filteredOptions.map((option, index) => {
                  const isSelected = multiple 
                    ? (value as string[]).includes(option.value)
                    : value === option.value;
                  const isFocused = index === focusedIndex;

                  return (
                    <li
                      key={option.value}
                      role="option"
                      aria-selected={isSelected}
                      onClick={() => !option.disabled && handleOptionSelect(option.value)}
                      className={cn(
                        'flex items-center px-3 py-2 text-sm cursor-pointer',
                        'hover:bg-gray-100 dark:hover:bg-gray-700',
                        isFocused && 'bg-gray-100 dark:bg-gray-700',
                        isSelected && 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300',
                        option.disabled && 'opacity-50 cursor-not-allowed'
                      )}
                    >
                      {/* Icon */}
                      {option.icon && (
                        <div className="mr-2 flex-shrink-0">
                          {option.icon}
                        </div>
                      )}

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="font-medium">{option.label}</div>
                        {option.description && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {option.description}
                          </div>
                        )}
                      </div>

                      {/* Selection Indicator */}
                      {isSelected && (
                        <Check className="h-4 w-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                      )}
                    </li>
                  );
                })
              )}
            </ul>
          </div>
        )}
      </div>

      {/* Selected Items (for multiple selection) */}
      {multiple && (selectedOptions as Option[]).length > 0 && (
        <div className="flex flex-wrap gap-1">
          {(selectedOptions as Option[]).map(option => (
            <span
              key={option.value}
              className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded"
            >
              {option.label}
              <button
                type="button"
                onClick={() => handleOptionSelect(option.value)}
                className="hover:text-blue-900 dark:hover:text-blue-100"
                aria-label={`Remove ${option.label}`}
              >
                <X className="h-3 w-3" />
              </button>
            </span>
          ))}
        </div>
      )}

      {/* Description */}
      {description && (
        <p 
          id={descriptionId}
          className="text-xs text-gray-500 dark:text-gray-400"
        >
          {description}
        </p>
      )}

      {/* Error Message */}
      {error && (
        <p 
          id={errorId}
          className="text-xs text-red-600 dark:text-red-400"
          role="alert"
          aria-live="polite"
        >
          {error}
        </p>
      )}
    </div>
  );
};

export default AccessibleFilterSelect;
