"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Trash2, AlertTriangle, X } from 'lucide-react';
import { Entity } from '@/types/entity';
import { adminDeleteEntity } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';

interface EntityDeleteModalProps {
  entity: Entity | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const EntityDeleteModal: React.FC<EntityDeleteModalProps> = ({
  entity,
  isOpen,
  onClose,
  onSuccess,
}) => {
  const { session } = useAuth();
  const [loading, setLoading] = useState(false);
  const [confirmationText, setConfirmationText] = useState('');
  const [error, setError] = useState<string | null>(null);

  const expectedConfirmationText = entity ? `DELETE ${entity.name}` : '';
  const isConfirmationValid = confirmationText === expectedConfirmationText;

  const handleDelete = async () => {
    if (!entity || !session?.access_token || !isConfirmationValid) return;

    try {
      setLoading(true);
      setError(null);

      await adminDeleteEntity(entity.id, session.access_token);
      
      onSuccess();
      onClose();
      setConfirmationText('');
    } catch (error: any) {
      console.error('Error deleting entity:', error);
      setError(error.message || 'Failed to delete entity. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setConfirmationText('');
      setError(null);
      onClose();
    }
  };

  if (!entity) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center text-red-600">
            <AlertTriangle className="w-5 h-5 mr-2" />
            Delete Entity
          </DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete the entity and all associated data.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <strong>Warning:</strong> Deleting this entity will:
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Permanently remove all entity data</li>
                <li>Delete all associated reviews and ratings</li>
                <li>Remove all user bookmarks and upvotes</li>
                <li>Break any existing links to this entity</li>
              </ul>
            </AlertDescription>
          </Alert>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Entity to be deleted:</h4>
            <div className="flex items-center space-x-3">
              {entity.logoUrl && (
                <img 
                  src={entity.logoUrl} 
                  alt={entity.name}
                  className="w-10 h-10 rounded-lg object-cover"
                />
              )}
              <div>
                <p className="font-medium text-gray-900">{entity.name}</p>
                <p className="text-sm text-gray-500">{entity.entityType.name}</p>
                <p className="text-sm text-gray-500">
                  {entity.reviewCount} reviews • {entity.upvoteCount || 0} upvotes
                </p>
              </div>
            </div>
          </div>

          <div>
            <Label htmlFor="confirmation">
              Type <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">
                {expectedConfirmationText}
              </code> to confirm deletion:
            </Label>
            <Input
              id="confirmation"
              value={confirmationText}
              onChange={(e) => setConfirmationText(e.target.value)}
              placeholder={expectedConfirmationText}
              className="mt-1"
              disabled={loading}
            />
          </div>

          {error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {error}
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={loading || !isConfirmationValid}
          >
            {loading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Trash2 className="w-4 h-4 mr-2" />
            )}
            Delete Entity
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
