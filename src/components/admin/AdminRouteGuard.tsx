"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { AlertCircle, Shield, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface AdminRouteGuardProps {
  children: React.ReactNode;
  requiredPermission?: 'isAdmin' | 'isModerator' | 'isAdminOrModerator' | 'canManageUsers' | 'canManageEntities' | 'canManageSettings' | 'canViewAnalytics';
  fallbackPath?: string;
}

/**
 * Component that protects admin routes based on user permissions
 * Redirects unauthorized users or shows appropriate error messages
 */
export const AdminRouteGuard: React.FC<AdminRouteGuardProps> = ({
  children,
  requiredPermission = 'isAdminOrModerator',
  fallbackPath = '/',
}) => {
  const router = useRouter();
  const adminAuth = useAdminAuth();

  // Show loading state while checking authentication
  if (adminAuth.isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-indigo-600" />
          <p className="text-gray-600">Verifying admin access...</p>
        </div>
      </div>
    );
  }

  // Check if user has required permission
  const hasPermission = adminAuth[requiredPermission];

  if (!hasPermission) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="mb-6">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
              <Shield className="h-8 w-8 text-red-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h2>
            <p className="text-gray-600">
              You don't have permission to access this admin area.
            </p>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
              <div className="text-left">
                <h3 className="text-sm font-medium text-yellow-800 mb-1">
                  Admin Access Required
                </h3>
                <p className="text-sm text-yellow-700">
                  This area is restricted to administrators and moderators only. 
                  If you believe you should have access, please contact your system administrator.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <Button
              onClick={() => router.push(fallbackPath)}
              className="w-full"
            >
              Return to Home
            </Button>
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="w-full"
            >
              Go Back
            </Button>
          </div>

          {/* Debug info in development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-6 p-4 bg-gray-100 rounded-lg text-left">
              <h4 className="text-sm font-medium text-gray-800 mb-2">Debug Info:</h4>
              <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                {JSON.stringify({
                  userRole: adminAuth.user?.role,
                  requiredPermission,
                  hasPermission,
                  isAdmin: adminAuth.isAdmin,
                  isModerator: adminAuth.isModerator,
                }, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    );
  }

  // User has permission, render the protected content
  return <>{children}</>;
};
