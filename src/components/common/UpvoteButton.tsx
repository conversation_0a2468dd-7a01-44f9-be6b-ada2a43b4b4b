'use client';

import React, { useState, useEffect } from 'react';
import { ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

interface UpvoteButtonProps {
  entityId: string;
  initialUpvoteCount?: number; // Made optional to handle missing backend data
  isInitiallyUpvoted?: boolean; // Made optional to handle missing backend data
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'compact';
}

const UpvoteButton: React.FC<UpvoteButtonProps> = ({
  entityId,
  initialUpvoteCount = 0, // Default to 0 if not provided
  isInitiallyUpvoted = false, // Default to false if not provided
  className = '',
  size = 'md',
  variant = 'default'
}) => {
  const { session, upvotedEntityIds, isLoadingUpvotedIds, handleUpvote, handleRemoveUpvote } = useAuth();
  const router = useRouter();

  // Local state for optimistic updates - use a ref to track if we've made local changes
  const [upvoteCount, setUpvoteCount] = useState(initialUpvoteCount);
  const [isUpvoted, setIsUpvoted] = useState(isInitiallyUpvoted);
  const [isLoading, setIsLoading] = useState(false);
  const [hasLocalChanges, setHasLocalChanges] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Sync with global upvoted state only if we haven't made local changes
  useEffect(() => {
    if (!hasLocalChanges) {
      setIsUpvoted(upvotedEntityIds.has(entityId));
    }
  }, [upvotedEntityIds, entityId, hasLocalChanges]);

  // Reset local changes flag when the component mounts or entityId changes
  useEffect(() => {
    setHasLocalChanges(false);
    setUpvoteCount(initialUpvoteCount || 0); // Ensure we always have a number
    setIsUpvoted(isInitiallyUpvoted || false);
    setError(null); // Clear errors when entity changes
  }, [entityId, initialUpvoteCount, isInitiallyUpvoted]);

  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Clear any previous errors
    setError(null);

    // Redirect to login if not authenticated
    if (!session) {
      router.push('/login');
      return;
    }

    setIsLoading(true);

    try {
      if (isUpvoted) {
        // Remove upvote
        setIsUpvoted(false);
        setUpvoteCount(prev => Math.max(0, (prev || 0) - 1));
        setHasLocalChanges(true);
        await handleRemoveUpvote(entityId);
      } else {
        // Add upvote
        setIsUpvoted(true);
        setUpvoteCount(prev => (prev || 0) + 1);
        setHasLocalChanges(true);
        await handleUpvote(entityId);
      }
    } catch (error) {
      console.error('Error toggling upvote:', error);

      // Set user-friendly error message
      const errorMessage = error instanceof Error ? error.message : 'Failed to update upvote. Please try again.';
      setError(errorMessage);

      // Revert optimistic updates on error
      setIsUpvoted(upvotedEntityIds.has(entityId));
      setUpvoteCount(initialUpvoteCount || 0);
      setHasLocalChanges(false);

      // Clear error after 5 seconds
      setTimeout(() => setError(null), 5000);
    } finally {
      setIsLoading(false);
    }
  };

  // Size configurations
  const sizeConfig = {
    sm: {
      button: 'h-7 px-2 text-xs',
      icon: 'h-3 w-3',
      gap: 'gap-1'
    },
    md: {
      button: 'h-8 px-3 text-sm',
      icon: 'h-4 w-4',
      gap: 'gap-1.5'
    },
    lg: {
      button: 'h-10 px-4 text-base',
      icon: 'h-5 w-5',
      gap: 'gap-2'
    }
  };

  const config = sizeConfig[size];

  if (variant === 'compact') {
    return (
      <div className="relative">
        <Button
          size="sm"
          variant={isUpvoted ? "default" : "outline"}
          className={`${config.button} ${config.gap} ${className} ${
            isUpvoted
              ? 'bg-indigo-600 text-white hover:bg-indigo-700'
              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
          } ${error ? 'border-red-300' : ''} transition-colors duration-150`}
          onClick={handleClick}
          disabled={isLoading || isLoadingUpvotedIds}
          aria-label={isUpvoted ? "Remove upvote" : "Add upvote"}
          title={error || undefined}
        >
          {(isLoading || isLoadingUpvotedIds) ? (
            <div className={`${config.icon} border border-gray-300 border-t-gray-600 rounded-full animate-spin`} />
          ) : (
            <ChevronUp
              className={`${config.icon} ${isUpvoted ? 'fill-current' : ''}`}
            />
          )}
          <span className="font-medium">{upvoteCount || 0}</span>
        </Button>
        {error && (
          <div className="absolute top-full left-0 mt-1 p-2 bg-red-100 border border-red-300 rounded text-xs text-red-700 z-10 whitespace-nowrap">
            {error}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`flex flex-col items-center ${className} relative`}>
      <Button
        size="sm"
        variant="ghost"
        className={`${config.button} ${config.gap} p-2 ${
          isUpvoted
            ? 'text-indigo-600 bg-indigo-50 hover:bg-indigo-100'
            : 'text-gray-500 hover:text-indigo-600 hover:bg-indigo-50'
        } ${error ? 'border border-red-300' : ''} transition-colors duration-150`}
        onClick={handleClick}
        disabled={isLoading || isLoadingUpvotedIds}
        aria-label={isUpvoted ? "Remove upvote" : "Add upvote"}
        title={error || undefined}
      >
        {(isLoading || isLoadingUpvotedIds) ? (
          <div className={`${config.icon} border border-gray-300 border-t-gray-600 rounded-full animate-spin`} />
        ) : (
          <ChevronUp
            className={`${config.icon} ${isUpvoted ? 'fill-current' : ''}`}
          />
        )}
      </Button>
      <span className={`text-xs font-medium mt-1 ${
        isUpvoted ? 'text-indigo-600' : 'text-gray-500'
      }`}>
        {upvoteCount || 0}
      </span>
      {error && (
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 p-2 bg-red-100 border border-red-300 rounded text-xs text-red-700 z-10 whitespace-nowrap">
          {error}
        </div>
      )}
    </div>
  );
};

export default UpvoteButton;
