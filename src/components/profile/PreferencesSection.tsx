'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Settings,
  Bell,
  Shield,
  Eye,
  Save,
  RefreshCw
} from 'lucide-react';
import { CompleteProfileData, UserPreferences, PreferencesUpdatePayload } from '@/types/profile';
import { getUserPreferences, updateUserPreferences } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';


interface PreferencesSectionProps {
  profileData: CompleteProfileData;
  onUpdate: (data: CompleteProfileData) => void;
}

export default function PreferencesSection({ profileData, onUpdate }: PreferencesSectionProps) {
  const { session, isLoading: isAuthLoading } = useAuth();
  const [preferences, setPreferences] = useState<UserPreferences | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Fetch preferences
  useEffect(() => {
    const fetchPreferences = async () => {
      // DO NOT FETCH if auth state is still being determined
      if (isAuthLoading) {
        return;
      }

      if (!session?.access_token) {
        setPreferences(null);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const prefs = await getUserPreferences(session.access_token);
        console.log('[PreferencesSection] Successfully fetched preferences:', prefs);
        setPreferences(prefs);
      } catch (apiError) {
        console.error('[PreferencesSection] Preferences API failed:', apiError);

        // Don't show error for authentication issues
        if (apiError instanceof Error && apiError.message.includes('Unauthorized')) {
          console.log('[PreferencesSection] Authentication error, will show loading state');
          setPreferences(null);
        } else if (apiError instanceof Error && apiError.message.includes('Preferences not found')) {
          // Handle case where user doesn't have preferences yet - create default ones
          console.log('[PreferencesSection] No preferences found, will create defaults');
          const defaultPreferences: UserPreferences = {
            id: '',
            user_id: '',
            email_notifications: true,
            marketing_emails: false,
            weekly_digest: true,
            new_tool_alerts: true,
            profile_visibility: 'public',
            show_bookmarks: true,
            show_reviews: true,
            show_activity: true,
            theme: 'system',
            items_per_page: 20,
            default_view: 'grid',
            preferred_categories: [],
            blocked_categories: [],
            content_language: 'en',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };
          setPreferences(defaultPreferences);
          setHasChanges(true); // Mark as having changes so user can save
        } else {
          setError(apiError instanceof Error ? apiError.message : 'Failed to load preferences');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchPreferences();
  }, [session?.access_token, isAuthLoading]); // Add isAuthLoading to dependency array

  // Handle preference changes
  const handlePreferenceChange = (key: keyof UserPreferences, value: UserPreferences[keyof UserPreferences]) => {
    if (!preferences) return;

    setPreferences(prev => prev ? { ...prev, [key]: value } : null);
    setHasChanges(true);
  };

  // Save preferences
  const handleSave = async () => {
    if (!session?.access_token || !preferences) return;

    try {
      setIsSaving(true);
      setError(null);

      const updatePayload: PreferencesUpdatePayload = {
        email_notifications: preferences.email_notifications,
        marketing_emails: preferences.marketing_emails,
        weekly_digest: preferences.weekly_digest,
        new_tool_alerts: preferences.new_tool_alerts,
        profile_visibility: preferences.profile_visibility,
        show_bookmarks: preferences.show_bookmarks,
        show_reviews: preferences.show_reviews,
        show_activity: preferences.show_activity,
        theme: preferences.theme,
        items_per_page: preferences.items_per_page,
        default_view: preferences.default_view,
        preferred_categories: preferences.preferred_categories,
        blocked_categories: preferences.blocked_categories,
        content_language: preferences.content_language,
      };

      const updatedPreferences = await updateUserPreferences(updatePayload, session.access_token);
      setPreferences(updatedPreferences);
      setHasChanges(false);

      // Update profile data
      onUpdate({
        ...profileData,
        preferences: updatedPreferences,
      });
    } catch (err) {
      console.error('Failed to update preferences:', err);
      setError(err instanceof Error ? err.message : 'Failed to update preferences');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-64" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, j) => (
                  <div key={j} className="flex items-center justify-between">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-12" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <div className="text-red-500">
              <Settings className="h-12 w-12 mx-auto mb-4" />
            </div>
            <h3 className="text-lg font-semibold">Failed to Load Preferences</h3>
            <p className="text-gray-600">{error}</p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!preferences) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <div className="text-gray-400">
              <Settings className="h-12 w-12 mx-auto mb-4" />
            </div>
            <h3 className="text-lg font-semibold">Loading Preferences...</h3>
            <p className="text-gray-600">Please wait while we load your preferences.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Save Button */}
      {hasChanges && (
        <div className="flex justify-end">
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      )}

      {/* Notification Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Bell className="h-5 w-5 mr-2" />
            Notification Preferences
          </CardTitle>
          <CardDescription>
            Manage how and when you receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="email_notifications">Email Notifications</Label>
              <p className="text-sm text-gray-600">Receive important updates via email</p>
            </div>
            <Checkbox
              id="email_notifications"
              checked={preferences.email_notifications}
              onCheckedChange={(checked) => 
                handlePreferenceChange('email_notifications', checked)
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="marketing_emails">Marketing Emails</Label>
              <p className="text-sm text-gray-600">Receive newsletters and promotional content</p>
            </div>
            <Checkbox
              id="marketing_emails"
              checked={preferences.marketing_emails}
              onCheckedChange={(checked) => 
                handlePreferenceChange('marketing_emails', checked)
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="weekly_digest">Weekly Digest</Label>
              <p className="text-sm text-gray-600">Get a summary of new tools and updates</p>
            </div>
            <Checkbox
              id="weekly_digest"
              checked={preferences.weekly_digest}
              onCheckedChange={(checked) => 
                handlePreferenceChange('weekly_digest', checked)
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="new_tool_alerts">New Tool Alerts</Label>
              <p className="text-sm text-gray-600">Get notified when new tools are added</p>
            </div>
            <Checkbox
              id="new_tool_alerts"
              checked={preferences.new_tool_alerts}
              onCheckedChange={(checked) => 
                handlePreferenceChange('new_tool_alerts', checked)
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Privacy Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="h-5 w-5 mr-2" />
            Privacy Settings
          </CardTitle>
          <CardDescription>
            Control who can see your profile and activity
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="profile_visibility">Profile Visibility</Label>
            <Select 
              value={preferences.profile_visibility} 
              onValueChange={(value: 'public' | 'private' | 'friends') => handlePreferenceChange('profile_visibility', value)}
            >
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="public">Public - Anyone can view</SelectItem>
                <SelectItem value="private">Private - Only you can view</SelectItem>
                <SelectItem value="friends">Friends - Only connections can view</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="show_bookmarks">Show Bookmarks</Label>
              <p className="text-sm text-gray-600">Allow others to see your bookmarked tools</p>
            </div>
            <Checkbox
              id="show_bookmarks"
              checked={preferences.show_bookmarks}
              onCheckedChange={(checked) => 
                handlePreferenceChange('show_bookmarks', checked)
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="show_reviews">Show Reviews</Label>
              <p className="text-sm text-gray-600">Display your reviews on your profile</p>
            </div>
            <Checkbox
              id="show_reviews"
              checked={preferences.show_reviews}
              onCheckedChange={(checked) => 
                handlePreferenceChange('show_reviews', checked)
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="show_activity">Show Activity</Label>
              <p className="text-sm text-gray-600">Display your recent activity</p>
            </div>
            <Checkbox
              id="show_activity"
              checked={preferences.show_activity}
              onCheckedChange={(checked) => 
                handlePreferenceChange('show_activity', checked)
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Display Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Eye className="h-5 w-5 mr-2" />
            Display Preferences
          </CardTitle>
          <CardDescription>
            Customize how content is displayed
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="theme">Theme</Label>
            <Select 
              value={preferences.theme} 
              onValueChange={(value: 'light' | 'dark' | 'system') => handlePreferenceChange('theme', value)}
            >
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="light">Light</SelectItem>
                <SelectItem value="dark">Dark</SelectItem>
                <SelectItem value="system">System</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="default_view">Default View</Label>
            <Select 
              value={preferences.default_view} 
              onValueChange={(value: 'grid' | 'list') => handlePreferenceChange('default_view', value)}
            >
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="grid">Grid View</SelectItem>
                <SelectItem value="list">List View</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="items_per_page">Items Per Page</Label>
            <Select 
              value={preferences.items_per_page.toString()} 
              onValueChange={(value) => handlePreferenceChange('items_per_page', parseInt(value))}
            >
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="content_language">Content Language</Label>
            <Select 
              value={preferences.content_language} 
              onValueChange={(value) => handlePreferenceChange('content_language', value)}
            >
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="es">Spanish</SelectItem>
                <SelectItem value="fr">French</SelectItem>
                <SelectItem value="de">German</SelectItem>
                <SelectItem value="zh">Chinese</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
