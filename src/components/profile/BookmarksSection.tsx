'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Search,
  Grid,
  List,
  Bookmark,
  ExternalLink,
  Trash2,
  Tag
} from 'lucide-react';
import { Entity } from '@/types/entity';
import { getBookmarkedEntities, unbookmarkEntity } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';

export default function BookmarksSection() {
  const { session, isLoading: isAuthLoading } = useAuth();
  const [bookmarks, setBookmarks] = useState<Entity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'category'>('date');

  // Fetch bookmarks
  useEffect(() => {
    const fetchBookmarks = async () => {
      // DO NOT FETCH if auth state is still being determined
      if (isAuthLoading) {
        return;
      }

      if (!session?.access_token) {
        setBookmarks([]);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        const response = await getBookmarkedEntities(session.access_token, 1, 100);

        // CRITICAL FIX: Check for valid response structure
        if (response && response.data && Array.isArray(response.data)) {
          setBookmarks(response.data);
        } else {
          console.error('Invalid response structure from getBookmarkedEntities', response);
          setBookmarks([]);
        }
      } catch (err) {
        console.error('Failed to fetch bookmarks:', err);

        // Don't show error for authentication issues - let the parent handle it
        if (err instanceof Error && err.message.includes('Unauthorized')) {
          console.log('[BookmarksSection] Authentication error, skipping error display');
          setBookmarks([]); // Set empty bookmarks instead of showing error
        } else {
          setError(err instanceof Error ? err.message : 'Failed to load bookmarks');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchBookmarks();
  }, [session?.access_token, isAuthLoading]); // Add isAuthLoading to dependency array

  // Handle unbookmark
  const handleUnbookmark = async (entityId: string) => {
    if (!session?.access_token) return;

    try {
      await unbookmarkEntity(entityId, session.access_token);
      setBookmarks(prev => prev.filter(bookmark => bookmark.id !== entityId));
    } catch (err) {
      console.error('Failed to remove bookmark:', err);
    }
  };

  // Filter and sort bookmarks
  const filteredBookmarks = bookmarks
    .filter(bookmark => {
      const matchesSearch = bookmark.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           bookmark.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' ||
                             (bookmark.categories || []).some(cat => cat.name === selectedCategory);
      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'category':
          return ((a.categories || [])[0]?.name || '').localeCompare(((b.categories || [])[0]?.name || ''));
        case 'date':
        default:
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
    });

  // Get unique categories
  const categories = Array.from(
    new Set(bookmarks.flatMap(bookmark =>
      (bookmark.categories || []).map(cat => cat.name)
    ))
  );

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <Skeleton className="h-6 w-32 mb-2" />
                <Skeleton className="h-4 w-48" />
              </div>
              <Skeleton className="h-10 w-24" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <Skeleton key={i} className="h-48 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <div className="text-red-500">
              <Bookmark className="h-12 w-12 mx-auto mb-4" />
            </div>
            <h3 className="text-lg font-semibold">Failed to Load Bookmarks</h3>
            <p className="text-gray-600">{error}</p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div>
              <CardTitle className="flex items-center">
                <Bookmark className="h-5 w-5 mr-2" />
                My Bookmarks ({bookmarks.length})
              </CardTitle>
              <CardDescription>
                Manage your saved AI tools and resources
              </CardDescription>
            </div>
            
            {/* View Toggle */}
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search bookmarks..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={sortBy} onValueChange={(value: 'date' | 'name' | 'category') => setSortBy(value)}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Date Added</SelectItem>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="category">Category</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Bookmarks Grid/List */}
          {filteredBookmarks.length === 0 ? (
            <div className="text-center py-12">
              <Bookmark className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {searchTerm || selectedCategory !== 'all' ? 'No matching bookmarks' : 'No bookmarks yet'}
              </h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || selectedCategory !== 'all' 
                  ? 'Try adjusting your search or filters'
                  : 'Start bookmarking AI tools to see them here'
                }
              </p>
              {!searchTerm && selectedCategory === 'all' && (
                <Button asChild>
                  <Link href="/browse">Browse AI Tools</Link>
                </Button>
              )}
            </div>
          ) : (
            <div className={
              viewMode === 'grid' 
                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                : 'space-y-4'
            }>
              {filteredBookmarks.map((bookmark) => (
                <div
                  key={bookmark.id}
                  className={`border rounded-lg p-4 hover:shadow-md transition-shadow ${
                    viewMode === 'list' ? 'flex items-center space-x-4' : ''
                  }`}
                >
                  {/* Entity Image/Icon */}
                  <div className={viewMode === 'list' ? 'flex-shrink-0' : 'mb-3'}>
                    {bookmark.logoUrl ? (
                      <img
                        src={bookmark.logoUrl}
                        alt={bookmark.name}
                        className={`rounded-lg object-cover ${
                          viewMode === 'list' ? 'h-12 w-12' : 'h-16 w-16'
                        }`}
                      />
                    ) : (
                      <div className={`bg-indigo-100 rounded-lg flex items-center justify-center ${
                        viewMode === 'list' ? 'h-12 w-12' : 'h-16 w-16'
                      }`}>
                        <Tag className={`text-indigo-600 ${
                          viewMode === 'list' ? 'h-6 w-6' : 'h-8 w-8'
                        }`} />
                      </div>
                    )}
                  </div>

                  {/* Content */}
                  <div className={viewMode === 'list' ? 'flex-1 min-w-0' : ''}>
                    <div className="flex items-start justify-between mb-2">
                      <h3 className={`font-semibold text-gray-900 ${
                        viewMode === 'list' ? 'text-base' : 'text-lg'
                      }`}>
                        <Link 
                          href={`/entities/${bookmark.slug}`}
                          className="hover:text-indigo-600 transition-colors"
                        >
                          {bookmark.name}
                        </Link>
                      </h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleUnbookmark(bookmark.id)}
                        className="text-gray-400 hover:text-red-500"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    <p className={`text-gray-600 mb-3 ${
                      viewMode === 'list' ? 'text-sm line-clamp-1' : 'text-sm line-clamp-2'
                    }`}>
                      {bookmark.description}
                    </p>
                    
                    {/* Categories and Actions */}
                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-1">
                        {(bookmark.categories || []).slice(0, 2).map((category) => (
                          <Badge key={category.id} variant="secondary" className="text-xs">
                            {category.name}
                          </Badge>
                        ))}
                        {(bookmark.categories || []).length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{(bookmark.categories || []).length - 2}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {bookmark.websiteUrl && (
                          <Button variant="ghost" size="sm" asChild>
                            <a
                              href={bookmark.websiteUrl}
                              target="_blank" 
                              rel="noopener noreferrer"
                            >
                              <ExternalLink className="h-4 w-4" />
                            </a>
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
