/**
 * End-to-end test for the Entity Submission Form
 * This test will help us understand the exact API contract without manual testing
 */

import { createEntity } from '@/services/api';
import { CreateEntityDto } from '@/types/entity';

// Mock data for testing (commented out as currently unused)
// const mockEntityTypes = [
//   {
//     id: 'ai-tool-type-id',
//     name: 'AI Tool',
//     slug: 'ai-tool',
//     description: 'AI-powered tools and applications'
//   }
// ];

// const mockCategories = [
//   {
//     id: 'category-1',
//     name: 'Productivity',
//     description: 'Tools for productivity'
//   }
// ];

// const mockTags = [
//   {
//     id: 'tag-1',
//     name: 'Machine Learning'
//   }
// ];

// const mockFeatures = [
//   {
//     id: 'feature-1',
//     name: 'API Access',
//     description: 'Provides API access'
//   }
// ];

// Test payload that matches our current form structure
const testPayload = {
  name: 'Test AI Tool',
  short_description: 'A test AI tool for validation',
  description: 'This is a comprehensive test of our AI tool submission system to validate the API contract.',
  website_url: 'https://example.com',
  logo_url: 'https://example.com/logo.png',
  documentation_url: 'https://docs.example.com',
  contact_url: 'https://example.com/contact',
  privacy_policy_url: 'https://example.com/privacy',
  founded_year: 2023,
  entity_type_id: 'ai-tool-type-id',
  category_ids: ['category-1'],
  tag_ids: ['tag-1'],
  feature_ids: ['feature-1'],
  ref_link: 'https://example.com/ref',
  meta_title: 'Test AI Tool',
  meta_description: 'A test AI tool for validation',
  tool_details: {
    // These fields are causing errors - let's test them one by one
    technical_level: 'INTERMEDIATE',
    has_api: true,
    has_free_tier: true,
    pricing_model: 'FREEMIUM',
    key_features: ['AI Analysis', 'Real-time Processing'],
    use_cases: ['Data Analysis', 'Content Generation'],
    pricing_details: 'Free tier available with premium features'
  }
};

// Minimal payload to test basic structure
const minimalPayload = {
  name: 'Minimal Test Tool',
  description: 'Minimal test description',
  website_url: 'https://example.com',
  entity_type_id: 'ai-tool-type-id',
  tool_details: {}
};

// Test different field combinations
const fieldTests = [
  {
    name: 'Empty tool_details',
    payload: { ...minimalPayload, tool_details: {} }
  },
  {
    name: 'Only has_free_tier',
    payload: { ...minimalPayload, tool_details: { has_free_tier: true } }
  },
  {
    name: 'Only technical_level',
    payload: { ...minimalPayload, tool_details: { technical_level: 'INTERMEDIATE' } }
  },
  {
    name: 'Only has_api',
    payload: { ...minimalPayload, tool_details: { has_api: true } }
  },
  {
    name: 'Only pricing_model',
    payload: { ...minimalPayload, tool_details: { pricing_model: 'FREEMIUM' } }
  },
  {
    name: 'Only arrays',
    payload: { 
      ...minimalPayload, 
      tool_details: { 
        key_features: ['Feature 1'], 
        use_cases: ['Use case 1'] 
      } 
    }
  }
];

/**
 * Test function to validate API contract
 */
async function testEntitySubmission() {
  console.log('🧪 Starting Entity Submission API Contract Tests\n');
  
  // You'll need to provide a valid token for testing
  const testToken = process.env.TEST_AUTH_TOKEN || 'your-test-token-here';
  
  if (!testToken || testToken === 'your-test-token-here') {
    console.log('❌ No test token provided. Set TEST_AUTH_TOKEN environment variable.');
    return;
  }

  for (const test of fieldTests) {
    console.log(`🔍 Testing: ${test.name}`);
    console.log(`📤 Payload:`, JSON.stringify(test.payload, null, 2));
    
    try {
      const result = await createEntity(test.payload as CreateEntityDto, testToken);
      console.log(`✅ Success:`, result);
    } catch (error) {
      console.log(`❌ Error:`, error instanceof Error ? error.message : error);
    }
    
    console.log('---\n');
  }
  
  console.log('🏁 Test completed');
}

// Export for use in other files
export { testEntitySubmission, fieldTests, testPayload, minimalPayload };

// Run test if this file is executed directly
if (require.main === module) {
  testEntitySubmission().catch(console.error);
}
