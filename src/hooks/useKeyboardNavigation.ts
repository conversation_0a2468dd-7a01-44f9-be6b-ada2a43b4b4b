import { useEffect, useCallback, useRef } from 'react';

interface KeyboardNavigationOptions {
  containerSelector?: string;
  focusableSelector?: string;
  onEscape?: () => void;
  onEnter?: (element: HTMLElement) => void;
  onArrowKey?: (direction: 'up' | 'down' | 'left' | 'right', element: HTMLElement) => void;
  trapFocus?: boolean;
  autoFocus?: boolean;
  skipDisabled?: boolean;
}

export function useKeyboardNavigation(options: KeyboardNavigationOptions = {}) {
  const {
    containerSelector = '[data-keyboard-nav]',
    focusableSelector = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
    onEscape,
    onEnter,
    onArrowKey,
    trapFocus = false,
    autoFocus = false,
    skipDisabled = true,
  } = options;

  const containerRef = useRef<HTMLElement | null>(null);
  const currentFocusIndex = useRef<number>(-1);

  // Get all focusable elements within the container
  const getFocusableElements = useCallback((): HTMLElement[] => {
    if (!containerRef.current) return [];
    
    const elements = Array.from(
      containerRef.current.querySelectorAll(focusableSelector)
    ) as HTMLElement[];
    
    return skipDisabled 
      ? elements.filter(el => !el.hasAttribute('disabled') && !el.getAttribute('aria-disabled'))
      : elements;
  }, [focusableSelector, skipDisabled]);

  // Focus element by index
  const focusElementByIndex = useCallback((index: number) => {
    const elements = getFocusableElements();
    if (elements.length === 0) return;
    
    const targetIndex = Math.max(0, Math.min(index, elements.length - 1));
    const element = elements[targetIndex];
    
    if (element) {
      element.focus();
      currentFocusIndex.current = targetIndex;
      
      // Scroll element into view if needed
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'nearest'
      });
    }
  }, [getFocusableElements]);

  // Move focus in a direction
  const moveFocus = useCallback((direction: 'next' | 'previous' | 'first' | 'last') => {
    const elements = getFocusableElements();
    if (elements.length === 0) return;
    
    let newIndex: number;
    
    switch (direction) {
      case 'next':
        newIndex = currentFocusIndex.current + 1;
        if (newIndex >= elements.length) {
          newIndex = trapFocus ? 0 : elements.length - 1;
        }
        break;
      case 'previous':
        newIndex = currentFocusIndex.current - 1;
        if (newIndex < 0) {
          newIndex = trapFocus ? elements.length - 1 : 0;
        }
        break;
      case 'first':
        newIndex = 0;
        break;
      case 'last':
        newIndex = elements.length - 1;
        break;
      default:
        return;
    }
    
    focusElementByIndex(newIndex);
  }, [getFocusableElements, trapFocus, focusElementByIndex]);

  // Handle keyboard events
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const target = event.target as HTMLElement;
    
    // Only handle events within our container
    if (!containerRef.current?.contains(target)) return;
    
    switch (event.key) {
      case 'Escape':
        if (onEscape) {
          event.preventDefault();
          onEscape();
        }
        break;
        
      case 'Enter':
      case ' ':
        if (onEnter && (target.tagName === 'BUTTON' || target.getAttribute('role') === 'button')) {
          event.preventDefault();
          onEnter(target);
        }
        break;
        
      case 'ArrowDown':
        event.preventDefault();
        if (onArrowKey) {
          onArrowKey('down', target);
        } else {
          moveFocus('next');
        }
        break;
        
      case 'ArrowUp':
        event.preventDefault();
        if (onArrowKey) {
          onArrowKey('up', target);
        } else {
          moveFocus('previous');
        }
        break;
        
      case 'ArrowRight':
        if (onArrowKey) {
          event.preventDefault();
          onArrowKey('right', target);
        }
        break;
        
      case 'ArrowLeft':
        if (onArrowKey) {
          event.preventDefault();
          onArrowKey('left', target);
        }
        break;
        
      case 'Home':
        event.preventDefault();
        moveFocus('first');
        break;
        
      case 'End':
        event.preventDefault();
        moveFocus('last');
        break;
        
      case 'Tab':
        if (trapFocus) {
          event.preventDefault();
          moveFocus(event.shiftKey ? 'previous' : 'next');
        }
        break;
    }
  }, [onEscape, onEnter, onArrowKey, moveFocus, trapFocus]);

  // Handle focus events to track current focus index
  const handleFocus = useCallback((event: FocusEvent) => {
    const target = event.target as HTMLElement;
    
    if (!containerRef.current?.contains(target)) return;
    
    const elements = getFocusableElements();
    const index = elements.indexOf(target);
    
    if (index !== -1) {
      currentFocusIndex.current = index;
    }
  }, [getFocusableElements]);

  // Initialize keyboard navigation
  const initializeNavigation = useCallback((container: HTMLElement | null) => {
    containerRef.current = container;
    
    if (container && autoFocus) {
      // Auto-focus first focusable element
      const elements = getFocusableElements();
      if (elements.length > 0) {
        focusElementByIndex(0);
      }
    }
  }, [autoFocus, getFocusableElements, focusElementByIndex]);

  // Set up event listeners
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('focus', handleFocus, true);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('focus', handleFocus, true);
    };
  }, [handleKeyDown, handleFocus]);

  // Public API
  return {
    initializeNavigation,
    moveFocus,
    focusElementByIndex,
    getFocusableElements,
    currentFocusIndex: currentFocusIndex.current,
  };
}

// Hook for managing focus within a specific component
export function useFocusManagement(containerRef: React.RefObject<HTMLElement>) {
  const previousFocus = useRef<HTMLElement | null>(null);
  
  // Save current focus when component mounts
  const saveFocus = useCallback(() => {
    previousFocus.current = document.activeElement as HTMLElement;
  }, []);
  
  // Restore previous focus when component unmounts
  const restoreFocus = useCallback(() => {
    if (previousFocus.current && typeof previousFocus.current.focus === 'function') {
      previousFocus.current.focus();
    }
  }, []);
  
  // Focus first element in container
  const focusFirst = useCallback(() => {
    if (!containerRef.current) return;
    
    const firstFocusable = containerRef.current.querySelector(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as HTMLElement;
    
    if (firstFocusable) {
      firstFocusable.focus();
    }
  }, [containerRef]);
  
  // Focus last element in container
  const focusLast = useCallback(() => {
    if (!containerRef.current) return;
    
    const focusableElements = Array.from(
      containerRef.current.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      )
    ) as HTMLElement[];
    
    const lastFocusable = focusableElements[focusableElements.length - 1];
    if (lastFocusable) {
      lastFocusable.focus();
    }
  }, [containerRef]);
  
  return {
    saveFocus,
    restoreFocus,
    focusFirst,
    focusLast,
  };
}

// Hook for managing modal/dialog focus trapping
export function useFocusTrap(isActive: boolean) {
  const containerRef = useRef<HTMLElement>(null);
  
  const { initializeNavigation } = useKeyboardNavigation({
    trapFocus: true,
    autoFocus: true,
    onEscape: () => {
      // Can be overridden by parent component
    }
  });
  
  useEffect(() => {
    if (isActive && containerRef.current) {
      initializeNavigation(containerRef.current);
    }
  }, [isActive, initializeNavigation]);
  
  return containerRef;
}
