import { useState, useCallback, useRef } from 'react';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

interface FilterCacheOptions {
  maxSize?: number;
  defaultTTL?: number; // Time to live in milliseconds
}

export function useFilterCache<T>(options: FilterCacheOptions = {}) {
  const { maxSize = 50, defaultTTL = 5 * 60 * 1000 } = options; // 5 minutes default
  
  const cache = useRef<Map<string, CacheEntry<T>>>(new Map());
  const [cacheStats, setCacheStats] = useState({
    hits: 0,
    misses: 0,
    size: 0,
  });

  const generateKey = useCallback((filters: Record<string, any>): string => {
    // Create a stable key from filter object
    const sortedEntries = Object.entries(filters)
      .filter(([_, value]) => value !== undefined && value !== null && value !== '')
      .sort(([a], [b]) => a.localeCompare(b));
    
    return JSON.stringify(sortedEntries);
  }, []);

  const isExpired = useCallback((entry: CacheEntry<T>): boolean => {
    return Date.now() > entry.expiresAt;
  }, []);

  const cleanupExpired = useCallback(() => {
    const now = Date.now();
    const toDelete: string[] = [];
    
    cache.current.forEach((entry, key) => {
      if (now > entry.expiresAt) {
        toDelete.push(key);
      }
    });
    
    toDelete.forEach(key => cache.current.delete(key));
    
    if (toDelete.length > 0) {
      setCacheStats(prev => ({
        ...prev,
        size: cache.current.size,
      }));
    }
  }, []);

  const evictOldest = useCallback(() => {
    if (cache.current.size === 0) return;
    
    let oldestKey = '';
    let oldestTime = Infinity;
    
    cache.current.forEach((entry, key) => {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    });
    
    if (oldestKey) {
      cache.current.delete(oldestKey);
      setCacheStats(prev => ({
        ...prev,
        size: cache.current.size,
      }));
    }
  }, []);

  const get = useCallback((filters: Record<string, any>): T | null => {
    const key = generateKey(filters);
    const entry = cache.current.get(key);
    
    if (!entry) {
      setCacheStats(prev => ({ ...prev, misses: prev.misses + 1 }));
      return null;
    }
    
    if (isExpired(entry)) {
      cache.current.delete(key);
      setCacheStats(prev => ({ 
        ...prev, 
        misses: prev.misses + 1,
        size: cache.current.size,
      }));
      return null;
    }
    
    setCacheStats(prev => ({ ...prev, hits: prev.hits + 1 }));
    return entry.data;
  }, [generateKey, isExpired]);

  const set = useCallback((filters: Record<string, any>, data: T, ttl?: number): void => {
    const key = generateKey(filters);
    const now = Date.now();
    const expiresAt = now + (ttl || defaultTTL);
    
    // Clean up expired entries first
    cleanupExpired();
    
    // If cache is full, evict oldest entry
    if (cache.current.size >= maxSize && !cache.current.has(key)) {
      evictOldest();
    }
    
    cache.current.set(key, {
      data,
      timestamp: now,
      expiresAt,
    });
    
    setCacheStats(prev => ({
      ...prev,
      size: cache.current.size,
    }));
  }, [generateKey, defaultTTL, maxSize, cleanupExpired, evictOldest]);

  const clear = useCallback(() => {
    cache.current.clear();
    setCacheStats({
      hits: 0,
      misses: 0,
      size: 0,
    });
  }, []);

  const remove = useCallback((filters: Record<string, any>): boolean => {
    const key = generateKey(filters);
    const deleted = cache.current.delete(key);
    
    if (deleted) {
      setCacheStats(prev => ({
        ...prev,
        size: cache.current.size,
      }));
    }
    
    return deleted;
  }, [generateKey]);

  const getHitRate = useCallback((): number => {
    const total = cacheStats.hits + cacheStats.misses;
    return total === 0 ? 0 : (cacheStats.hits / total) * 100;
  }, [cacheStats]);

  return {
    get,
    set,
    clear,
    remove,
    stats: {
      ...cacheStats,
      hitRate: getHitRate(),
    },
    cleanup: cleanupExpired,
  };
}
