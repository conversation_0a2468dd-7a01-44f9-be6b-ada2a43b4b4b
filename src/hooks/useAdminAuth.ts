import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/enums';
import { useMemo } from 'react';

export interface AdminAuthState {
  isAdmin: boolean;
  isModerator: boolean;
  isAdminOrModerator: boolean;
  canManageUsers: boolean;
  canManageEntities: boolean;
  canManageSettings: boolean;
  canViewAnalytics: boolean;
  isLoading: boolean;
  user: any;
}

/**
 * Custom hook for admin authentication and authorization
 * Provides role-based access control for admin features
 */
export const useAdminAuth = (): AdminAuthState => {
  const { user, isLoading } = useAuth();

  const adminState = useMemo(() => {
    if (isLoading || !user) {
      return {
        isAdmin: false,
        isModerator: false,
        isAdminOrModerator: false,
        canManageUsers: false,
        canManageEntities: false,
        canManageSettings: false,
        canViewAnalytics: false,
        isLoading,
        user: null,
      };
    }

    const isAdmin = user.role === UserRole.ADMIN;
    const isModerator = user.role === UserRole.MODERATOR;
    const isAdminOrModerator = isAdmin || isModerator;

    return {
      isAdmin,
      isModerator,
      isAdminOrModerator,
      // Admin permissions
      canManageUsers: isAdmin, // Only admins can manage users
      canManageEntities: isAdminOrModerator, // Both admins and moderators can manage entities
      canManageSettings: isAdmin, // Only admins can manage settings
      canViewAnalytics: isAdminOrModerator, // Both can view analytics
      isLoading: false,
      user,
    };
  }, [user, isLoading]);

  return adminState;
};

/**
 * Hook to check if user has specific admin permission
 */
export const useAdminPermission = (permission: keyof Omit<AdminAuthState, 'isLoading' | 'user'>) => {
  const adminAuth = useAdminAuth();
  return adminAuth[permission];
};
