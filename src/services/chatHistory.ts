'use client';

import { ChatMessage } from '@/types/chat';

export interface ChatConversation {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  messageCount: number;
  lastMessage: string;
}

export interface ChatHistoryService {
  getConversations(): ChatConversation[];
  getConversation(id: string): ChatConversation | null;
  saveConversation(conversation: ChatConversation): void;
  createNewConversation(): ChatConversation;
  addMessageToConversation(conversationId: string, message: ChatMessage): void;
  deleteConversation(id: string): void;
  updateConversationTitle(id: string, title: string): void;
  clearAllHistory(): void;
}

const STORAGE_KEY = 'ai_navigator_chat_history';
const MAX_CONVERSATIONS = 50; // Limit to prevent localStorage bloat
const MAX_MESSAGES_PER_CONVERSATION = 100;

class LocalChatHistoryService implements ChatHistoryService {
  private getStoredData(): ChatConversation[] {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (!stored) return [];
      
      const parsed = JSON.parse(stored);
      // Convert date strings back to Date objects
      return parsed.map((conv: any) => ({
        ...conv,
        createdAt: new Date(conv.createdAt),
        updatedAt: new Date(conv.updatedAt),
        messages: conv.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }))
      }));
    } catch (error) {
      console.error('Error loading chat history from localStorage:', error);
      return [];
    }
  }

  private saveToStorage(conversations: ChatConversation[]): void {
    try {
      // Limit the number of conversations
      const limitedConversations = conversations
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
        .slice(0, MAX_CONVERSATIONS);

      // Limit messages per conversation
      const processedConversations = limitedConversations.map(conv => ({
        ...conv,
        messages: conv.messages.slice(-MAX_MESSAGES_PER_CONVERSATION)
      }));

      localStorage.setItem(STORAGE_KEY, JSON.stringify(processedConversations));
    } catch (error) {
      console.error('Error saving chat history to localStorage:', error);
    }
  }

  getConversations(): ChatConversation[] {
    return this.getStoredData().sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  getConversation(id: string): ChatConversation | null {
    const conversations = this.getStoredData();
    return conversations.find(conv => conv.id === id) || null;
  }

  saveConversation(conversation: ChatConversation): void {
    const conversations = this.getStoredData();
    const existingIndex = conversations.findIndex(conv => conv.id === conversation.id);
    
    if (existingIndex >= 0) {
      conversations[existingIndex] = conversation;
    } else {
      conversations.push(conversation);
    }
    
    this.saveToStorage(conversations);
  }

  createNewConversation(): ChatConversation {
    const id = `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date();
    
    const newConversation: ChatConversation = {
      id,
      title: 'New Conversation',
      messages: [],
      createdAt: now,
      updatedAt: now,
      messageCount: 0,
      lastMessage: ''
    };

    this.saveConversation(newConversation);
    return newConversation;
  }

  addMessageToConversation(conversationId: string, message: ChatMessage): void {
    const conversation = this.getConversation(conversationId);
    if (!conversation) return;

    conversation.messages.push(message);
    conversation.messageCount = conversation.messages.length;
    conversation.lastMessage = message.text.substring(0, 100); // Truncate for preview
    conversation.updatedAt = new Date();

    // Auto-generate title from first user message
    if (conversation.title === 'New Conversation' && message.sender === 'user') {
      conversation.title = this.generateConversationTitle(message.text);
    }

    this.saveConversation(conversation);
  }

  deleteConversation(id: string): void {
    const conversations = this.getStoredData();
    const filtered = conversations.filter(conv => conv.id !== id);
    this.saveToStorage(filtered);
  }

  updateConversationTitle(id: string, title: string): void {
    const conversation = this.getConversation(id);
    if (!conversation) return;

    conversation.title = title;
    conversation.updatedAt = new Date();
    this.saveConversation(conversation);
  }

  clearAllHistory(): void {
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('Error clearing chat history:', error);
    }
  }

  private generateConversationTitle(firstMessage: string): string {
    // Generate a meaningful title from the first user message
    const cleaned = firstMessage.trim().replace(/\s+/g, ' ');
    const maxLength = 40;
    
    if (cleaned.length <= maxLength) {
      return cleaned;
    }
    
    // Try to cut at word boundary
    const truncated = cleaned.substring(0, maxLength);
    const lastSpace = truncated.lastIndexOf(' ');
    
    if (lastSpace > maxLength * 0.7) {
      return truncated.substring(0, lastSpace) + '...';
    }
    
    return truncated + '...';
  }
}

// Export singleton instance
export const chatHistoryService = new LocalChatHistoryService();

// Utility functions for working with chat history
export const formatConversationTime = (date: Date): string => {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return date.toLocaleDateString();
};

export const groupConversationsByDate = (conversations: ChatConversation[]) => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  const weekAgo = new Date(today);
  weekAgo.setDate(weekAgo.getDate() - 7);

  const groups: { [key: string]: ChatConversation[] } = {
    'Today': [],
    'Yesterday': [],
    'This Week': [],
    'Older': []
  };

  conversations.forEach(conv => {
    const convDate = new Date(conv.updatedAt.getFullYear(), conv.updatedAt.getMonth(), conv.updatedAt.getDate());
    
    if (convDate.getTime() === today.getTime()) {
      groups['Today'].push(conv);
    } else if (convDate.getTime() === yesterday.getTime()) {
      groups['Yesterday'].push(conv);
    } else if (convDate >= weekAgo) {
      groups['This Week'].push(conv);
    } else {
      groups['Older'].push(conv);
    }
  });

  return groups;
};
